# Delivery Arrangement API Tester

这个脚本用于测试delivery-arrangement API，使用Excel文件中的zipcode进行批量测试。

## 功能特性

- **OAuth认证**: 自动获取和刷新Microsoft OAuth token
- **并发控制**: 可配置的并发请求数量，避免API过载
- **进度跟踪**: 使用指针文件记录执行进度，支持断点续传
- **结果缓存**: 单次执行中缓存重复zipcode的结果
- **错误处理**: 完善的错误处理和重试机制
- **日志记录**: 详细的日志记录，便于调试和监控

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 复制配置文件模板：
```bash
cp .env.sample .env
```

2. 编辑 `.env` 文件，填入你的配置：
```
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here
CONCURRENCY=5

# File Configuration
INPUT_EXCEL_FILE=masterfile-0805.xlsx
OUTPUT_EXCEL_FILE=delivery_test_results.xlsx
PROGRESS_FILE=progress.json
RESPONSE_LOG_FILE=api_responses.json
```

## 使用方法

### 基本用法

```bash
# 从上次停止的地方继续执行
python delivery_test.py

# 从头开始执行（重置进度）
python delivery_test.py --reset-progress

# 指定行范围执行
python delivery_test.py --start-row 0 --end-row 1000

# 指定批处理大小
python delivery_test.py --batch-size 100
```

### 参数说明

- `--start-row`: 起始行号（从0开始）
- `--end-row`: 结束行号（不包含）
- `--batch-size`: 批处理大小，默认50
- `--reset-progress`: 重置进度，从头开始

### 输出文件

所有输出文件都保存在带时间戳的文件夹中：`results/test_YYYYMMDD_HHMMSS/`

- `delivery_test_YYYYMMDD_HHMMSS.log`: 详细的执行日志
- `progress_YYYYMMDD_HHMMSS.json`: 进度跟踪文件
- `delivery_test_results_YYYYMMDD_HHMMSS.xlsx`: 测试结果Excel文件
- `api_responses_YYYYMMDD_HHMMSS.json`: 完整的API响应日志

例如：`results/test_20250805_132933/`

### 输出文件详细说明

#### Excel结果列
脚本会在Excel文件中添加以下列：
- `test_status`: HTTP状态码
- `test_success`: 测试是否成功（True/False）
- `test_response_time`: 响应时间（秒）
- `test_delivery_count`: 可用配送服务数量
- `test_has_delivery`: 是否有配送选项（True/False）
- `test_service_methods`: 配送方式列表
- `test_capacity_available`: 是否有可用容量
- `test_full_response`: API响应摘要
- `test_error`: 错误信息（如果有）
- `test_error_type`: 错误类型
- `test_detailed_error`: 详细错误信息
- `test_timestamp`: 测试时间戳

#### 详细日志文件
- `api_responses.json`: 完整的API请求和响应日志，包含：
  - 请求头和响应头
  - 完整的API响应数据
  - 错误详情和堆栈跟踪
  - 性能指标

## 监控和调试

### 查看所有测试结果
```bash
python3 list_results.py
```

### 查看实时日志（最新测试）
```bash
tail -f results/test_*/delivery_test_*.log
```

### 查看进度（最新测试）
```bash
cat results/test_*/progress_*.json
```

### 分析结果
```bash
# 运行详细分析
python analyze_results.py

# 查看API响应详情
python -c "import json; print(json.dumps(json.load(open('api_responses.json')), indent=2))"

# 统计结果
grep "SUCCESS" delivery_test.log | wc -l  # 成功数量
grep "FAILED" delivery_test.log | wc -l   # 失败数量
```

## 注意事项

1. **API限制**: 脚本默认并发数为5，可根据API承受能力调整
2. **Token过期**: OAuth token有效期1小时，脚本会自动刷新
3. **断点续传**: 可以随时中断脚本，下次运行会从上次停止的地方继续
4. **重复zipcode**: 单次执行中重复的zipcode会使用缓存结果，不会重复调用API
5. **错误处理**: 网络错误和API错误都会被记录，不会中断整个流程

## 故障排除

### 常见问题

1. **认证失败**: 检查CLIENT_ID和CLIENT_SECRET是否正确
2. **Excel文件锁定**: 确保Excel文件没有被其他程序打开
3. **网络超时**: 检查网络连接，必要时调整超时设置
4. **权限错误**: 确保有读写Excel文件和创建日志文件的权限

### 管理测试结果

```bash
# 查看所有测试结果
python3 list_results.py

# 清理旧结果（保留最新5个）
python3 list_results.py clean

# 清理旧结果（保留最新3个）
python3 list_results.py clean 3
```

### 重新开始测试

每次运行都会创建新的时间戳文件夹，原有结果不会被覆盖：
```bash
python3 delivery_test.py --reset-progress
```
