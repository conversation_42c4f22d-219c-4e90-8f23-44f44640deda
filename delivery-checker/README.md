# Delivery Arrangement API Tester

这个脚本用于测试delivery-arrangement API，使用Excel文件中的zipcode进行批量测试。

## 功能特性

- **OAuth认证**: 自动获取和刷新Microsoft OAuth token
- **并发控制**: 可配置的并发请求数量，避免API过载
- **进度跟踪**: 使用指针文件记录执行进度，支持断点续传
- **结果缓存**: 单次执行中缓存重复zipcode的结果
- **错误处理**: 完善的错误处理和重试机制
- **日志记录**: 详细的日志记录，便于调试和监控

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 复制配置文件模板：
```bash
cp .env.sample .env
```

2. 编辑 `.env` 文件，填入你的配置：
```
CLIENT_ID=your_client_id_here
CLIENT_SECRET=your_client_secret_here
CONCURRENCY=5
EXCEL_FILE=masterfile-0805.xlsx
PROGRESS_FILE=progress.json
```

## 使用方法

### 基本用法

```bash
# 从上次停止的地方继续执行
python delivery_test.py

# 从头开始执行（重置进度）
python delivery_test.py --reset-progress

# 指定行范围执行
python delivery_test.py --start-row 0 --end-row 1000

# 指定批处理大小
python delivery_test.py --batch-size 100
```

### 参数说明

- `--start-row`: 起始行号（从0开始）
- `--end-row`: 结束行号（不包含）
- `--batch-size`: 批处理大小，默认50
- `--reset-progress`: 重置进度，从头开始

### 输出文件

- `delivery_test.log`: 详细的执行日志
- `progress.json`: 进度跟踪文件
- `masterfile-0805.xlsx`: 更新后的Excel文件，包含测试结果

### Excel结果列

脚本会在Excel文件中添加以下列：
- `test_status`: HTTP状态码
- `test_success`: 测试是否成功（True/False）
- `test_response_time`: 响应时间（秒）
- `test_error`: 错误信息（如果有）
- `test_timestamp`: 测试时间戳

## 监控和调试

### 查看实时日志
```bash
tail -f delivery_test.log
```

### 查看进度
```bash
cat progress.json
```

### 统计结果
```bash
# 成功的测试数量
grep "SUCCESS" delivery_test.log | wc -l

# 失败的测试数量
grep "FAILED" delivery_test.log | wc -l
```

## 注意事项

1. **API限制**: 脚本默认并发数为5，可根据API承受能力调整
2. **Token过期**: OAuth token有效期1小时，脚本会自动刷新
3. **断点续传**: 可以随时中断脚本，下次运行会从上次停止的地方继续
4. **重复zipcode**: 单次执行中重复的zipcode会使用缓存结果，不会重复调用API
5. **错误处理**: 网络错误和API错误都会被记录，不会中断整个流程

## 故障排除

### 常见问题

1. **认证失败**: 检查CLIENT_ID和CLIENT_SECRET是否正确
2. **Excel文件锁定**: 确保Excel文件没有被其他程序打开
3. **网络超时**: 检查网络连接，必要时调整超时设置
4. **权限错误**: 确保有读写Excel文件和创建日志文件的权限

### 重新开始测试

如果需要完全重新开始测试：
```bash
rm progress.json
python delivery_test.py --reset-progress
```
