#!/usr/bin/env python3
"""
Analyze delivery test results and API responses
"""

import json
import pandas as pd
from collections import Counter
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def find_latest_results_dir():
    """Find the latest results directory"""
    output_dir = os.getenv('OUTPUT_DIR', 'results')
    if not os.path.exists(output_dir):
        return None

    # Find all test directories
    test_dirs = [d for d in os.listdir(output_dir) if d.startswith('test_')]
    if not test_dirs:
        return None

    # Return the latest one (sorted by name which includes timestamp)
    latest_dir = sorted(test_dirs)[-1]
    return os.path.join(output_dir, latest_dir)

def analyze_api_responses(results_dir=None):
    """Analyze detailed API responses from JSON log"""
    print("🔍 Analyzing API Responses")
    print("=" * 50)

    if results_dir is None:
        results_dir = find_latest_results_dir()
        if results_dir is None:
            print("❌ No results directory found. Run the test first.")
            return []

    base_response_log = os.getenv('RESPONSE_LOG_FILE', 'api_responses.json')

    # Look for timestamped response log files in the results directory
    response_files = []
    if os.path.exists(results_dir):
        for file in os.listdir(results_dir):
            if file.startswith('api_responses_') and file.endswith('.json'):
                response_files.append(os.path.join(results_dir, file))

    if not response_files:
        print(f"❌ No API response files found in {results_dir}")
        return []

    # Use the latest response file
    response_log_file = sorted(response_files)[-1]
    print(f"📁 Using response file: {response_log_file}")

    try:
        with open(response_log_file, 'r') as f:
            responses = json.load(f)
        
        print(f"📊 Total API calls logged: {len(responses)}")
        
        # Analyze success/failure
        successful = [r for r in responses if r['success']]
        failed = [r for r in responses if not r['success']]
        
        print(f"✅ Successful calls: {len(successful)}")
        print(f"❌ Failed calls: {len(failed)}")
        
        # Analyze response times
        response_times = [r['response_time'] for r in responses if r['response_time']]
        if response_times:
            print(f"⏱️  Average response time: {sum(response_times)/len(response_times):.3f}s")
            print(f"⏱️  Min response time: {min(response_times):.3f}s")
            print(f"⏱️  Max response time: {max(response_times):.3f}s")
        
        # Analyze API errors
        api_errors = []
        for r in successful:  # Even successful HTTP calls can have API errors
            if r['response_data'] and 'error' in r['response_data']:
                error_info = r['response_data']['error']
                api_errors.append({
                    'zipcode': r['zipcode'],
                    'error_code': error_info.get('errorCode', 'Unknown'),
                    'error_description': error_info.get('errorDescription', 'Unknown')
                })
        
        if api_errors:
            print(f"\n🚨 API Errors Found: {len(api_errors)}")
            error_codes = Counter([e['error_code'] for e in api_errors])
            for code, count in error_codes.most_common():
                print(f"  {code}: {count} occurrences")
                # Show sample error description
                sample_error = next(e for e in api_errors if e['error_code'] == code)
                print(f"    Description: {sample_error['error_description']}")
        
        # Analyze successful delivery responses
        delivery_responses = []
        ship_nodes = []
        merge_nodes = []
        transport_methods = []

        for r in successful:
            if (r['response_data'] and
                'serviceTypes' in r['response_data'] and
                r['response_data']['serviceTypes'].get('serviceType')):
                delivery_responses.append(r)

                # Extract delivery details
                service_types = r['response_data']['serviceTypes']['serviceType']
                for service in service_types:
                    solutions = service.get('possibleSolutions', {}).get('possibleSolution', [])
                    for solution in solutions:
                        delivery_lines = solution.get('deliveryLines', {}).get('deliveryLine', [])
                        for line in delivery_lines:
                            if line.get('shipNode'):
                                ship_nodes.append(line['shipNode'])
                            if line.get('mergeNode'):
                                merge_nodes.append(line['mergeNode'])
                            if line.get('transportMethodType'):
                                transport_methods.append(line['transportMethodType'])

        print(f"\n🚚 Responses with delivery services: {len(delivery_responses)}")

        if delivery_responses:
            print("📊 Delivery Infrastructure Analysis:")
            if ship_nodes:
                ship_node_counts = Counter(ship_nodes)
                print(f"  Ship Nodes: {len(set(ship_nodes))} unique")
                for node, count in ship_node_counts.most_common(5):
                    print(f"    {node}: {count} times")

            if merge_nodes:
                merge_node_counts = Counter(merge_nodes)
                print(f"  Merge Nodes: {len(set(merge_nodes))} unique")
                for node, count in merge_node_counts.most_common(5):
                    print(f"    {node}: {count} times")

            if transport_methods:
                transport_counts = Counter(transport_methods)
                print(f"  Transport Methods: {', '.join(set(transport_methods))}")
                for method, count in transport_counts.items():
                    print(f"    {method}: {count} times")

            print("\nSample delivery services:")
            for r in delivery_responses[:3]:  # Show first 3
                service_types = r['response_data']['serviceTypes']['serviceType']
                print(f"  Zipcode {r['zipcode']}: {len(service_types)} service types")
                for service in service_types:
                    service_id = service.get('id', 'Unknown')
                    solutions = service.get('possibleSolutions', {}).get('possibleSolution', [])
                    print(f"    - {service_id} ({len(solutions)} solutions)")
        
        return responses

    except FileNotFoundError:
        print(f"❌ {response_log_file} not found. Run the test first.")
        return []
    except Exception as e:
        print(f"❌ Error analyzing responses: {e}")
        return []

def analyze_excel_results(results_dir=None):
    """Analyze results from Excel file"""
    print(f"\n📊 Analyzing Excel Results")
    print("=" * 50)

    if results_dir is None:
        results_dir = find_latest_results_dir()
        if results_dir is None:
            print("❌ No results directory found. Run the test first.")
            return

    # Look for timestamped Excel files in the results directory
    excel_files = []
    if os.path.exists(results_dir):
        for file in os.listdir(results_dir):
            if file.startswith('delivery_test_results_') and file.endswith('.xlsx'):
                excel_files.append(os.path.join(results_dir, file))

    if not excel_files:
        print(f"❌ No Excel result files found in {results_dir}")
        return

    # Use the latest Excel file
    output_excel_file = sorted(excel_files)[-1]
    print(f"📁 Using Excel file: {output_excel_file}")

    try:
        df = pd.read_excel(output_excel_file)
        
        # Filter rows that have been tested
        tested_df = df[df['test_status'].notna()]
        
        if len(tested_df) == 0:
            print("❌ No test results found in Excel file")
            return
        
        print(f"📊 Total rows tested: {len(tested_df)}")
        print(f"✅ Successful tests: {tested_df['test_success'].sum()}")
        print(f"❌ Failed tests: {(~tested_df['test_success']).sum()}")
        
        # Response time analysis
        if 'test_response_time' in tested_df.columns:
            response_times = tested_df['test_response_time'].dropna()
            if len(response_times) > 0:
                print(f"⏱️  Average response time: {response_times.mean():.3f}s")
                print(f"⏱️  Response time range: {response_times.min():.3f}s - {response_times.max():.3f}s")
        
        # Delivery service analysis
        if 'test_delivery_count' in tested_df.columns:
            delivery_counts = tested_df['test_delivery_count'].dropna()
            if len(delivery_counts) > 0:
                print(f"🚚 Average delivery services per zipcode: {delivery_counts.mean():.1f}")
                print(f"🚚 Max delivery services: {delivery_counts.max()}")
                
                # Count zipcodes with delivery options
                with_delivery = tested_df[tested_df['test_has_delivery'] == True]
                print(f"🚚 Zipcodes with delivery options: {len(with_delivery)}")
        
        # Error analysis
        if 'test_error_type' in tested_df.columns:
            error_types = tested_df['test_error_type'].dropna()
            if len(error_types) > 0:
                print(f"\n🚨 Error Types:")
                error_counts = error_types.value_counts()
                for error_type, count in error_counts.items():
                    print(f"  {error_type}: {count} occurrences")
        
        # Show sample results
        print(f"\n📋 Sample Results:")
        display_cols = ['ZipCode', 'test_status', 'test_success', 'test_response_time', 
                       'test_delivery_count', 'test_has_delivery']
        available_cols = [col for col in display_cols if col in tested_df.columns]
        print(tested_df[available_cols].head(10).to_string(index=False))

    except FileNotFoundError:
        print(f"❌ {output_excel_file} not found. Run the test first to generate results.")
    except Exception as e:
        print(f"❌ Error analyzing Excel results: {e}")

def show_recommendations():
    """Show recommendations based on analysis"""
    print(f"\n💡 Recommendations")
    print("=" * 50)
    
    print("1. 🔧 API Configuration:")
    print("   - The test item ID '10534224' doesn't exist (YFS10437 error)")
    print("   - Consider using a valid item ID for testing")
    print("   - Check with the API team for valid test item IDs")
    
    print("\n2. 📊 Testing Strategy:")
    print("   - Current tests show API connectivity is working")
    print("   - HTTP 200 responses with API-level errors indicate authentication is correct")
    print("   - Consider testing with different item IDs to get delivery options")
    
    print("\n3. 🚀 Next Steps:")
    print("   - Update the item ID in the payload")
    print("   - Test with a few zipcodes that are known to have delivery services")
    print("   - Run larger batches once item ID is corrected")
    
    print("\n4. 📈 Performance:")
    print("   - Current response times (~0.08-0.10s) are excellent")
    print("   - Concurrency of 5 seems appropriate")
    print("   - Rate limits show plenty of capacity (1194/1200 remaining)")

def main():
    """Main analysis function"""
    print("🔍 Delivery Test Results Analysis")
    print("=" * 60)

    # Find latest results directory
    results_dir = find_latest_results_dir()
    if results_dir:
        print(f"📁 Analyzing results from: {results_dir}")
    else:
        print("❌ No results found. Please run the test first.")
        return

    # Analyze API responses
    responses = analyze_api_responses(results_dir)

    # Analyze Excel results
    analyze_excel_results(results_dir)

    # Show recommendations
    show_recommendations()

    print(f"\n{'='*60}")
    print("Analysis completed! 📊")

if __name__ == '__main__':
    main()
