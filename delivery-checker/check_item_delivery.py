#!/usr/bin/env python3
"""
Check delivery for a specific item and zipcode
"""

import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()

def get_access_token():
    """获取访问令牌"""
    token_url = os.getenv('TOKEN_URL')
    client_id = os.getenv('CLIENT_ID')
    client_secret = os.getenv('CLIENT_SECRET')
    
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}
    data = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret
    }
    
    try:
        response = requests.post(token_url, headers=headers, data=data, timeout=30)
        if response.status_code == 200:
            return response.json()['access_token']
        else:
            print(f'❌ 获取token失败: {response.status_code} - {response.text}')
            return None
    except Exception as e:
        print(f'❌ 获取token异常: {e}')
        return None

def check_delivery_with_item(zipcode, item_id):
    """检查特定商品在指定zipcode的配送情况"""
    
    print(f'🔍 检查配送情况')
    print(f'商品ID: {item_id}')
    print(f'邮编: {zipcode}')
    print('=' * 50)
    
    # 获取访问令牌
    print('正在获取访问令牌...')
    access_token = get_access_token()
    if not access_token:
        return False
    
    print('✅ 访问令牌获取成功')
    
    # 构建API请求
    api_url = os.getenv('API_URL')
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        'zipCode': str(zipcode),
        'items': [
            {
                'itemId': str(item_id),
                'quantity': 1
            }
        ]
    }
    
    print(f'正在调用配送API...')
    print(f'请求数据: {json.dumps(payload, indent=2)}')
    
    try:
        response = requests.post(api_url, headers=headers, json=payload, timeout=30)
        
        print(f'\n📊 API响应结果:')
        print(f'状态码: {response.status_code}')
        print(f'响应时间: {response.elapsed.total_seconds():.3f}秒')
        
        if response.status_code == 200:
            result = response.json()
            print(f'\n✅ 请求成功!')
            
            # 解析配送信息
            if 'serviceTypes' in result and result['serviceTypes']:
                service_types = result['serviceTypes']
                if 'serviceType' in service_types and service_types['serviceType']:
                    print(f'\n🚚 配送服务详情:')
                    
                    services = service_types['serviceType']
                    if not isinstance(services, list):
                        services = [services]
                    
                    for i, service in enumerate(services, 1):
                        service_name = service.get('serviceTypeName', '未知服务')
                        print(f'\n服务 {i}: {service_name}')
                        
                        if 'solutions' in service and service['solutions']:
                            solutions = service['solutions']['solution']
                            if not isinstance(solutions, list):
                                solutions = [solutions]
                            
                            print(f'  配送方案数量: {len(solutions)}')
                            
                            for j, solution in enumerate(solutions, 1):
                                print(f'\n  方案 {j}:')
                                if 'deliveryLines' in solution:
                                    delivery_lines = solution['deliveryLines']['deliveryLine']
                                    if not isinstance(delivery_lines, list):
                                        delivery_lines = [delivery_lines]
                                    
                                    for k, line in enumerate(delivery_lines, 1):
                                        ship_node = line.get('shipNode', '未知')
                                        merge_node = line.get('mergeNode', '未知')
                                        transport = line.get('transportationMode', '未知')
                                        print(f'    配送线路 {k}: 发货节点={ship_node}, 合并节点={merge_node}, 运输方式={transport}')
                        else:
                            print(f'  ❌ 该服务暂无可用配送方案')
                else:
                    print(f'\n❌ 该地区暂无配送服务')
            else:
                print(f'\n❌ 响应中未找到配送服务信息')
            
            print(f'\n📋 完整响应数据:')
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            return True
                
        else:
            print(f'\n❌ 请求失败!')
            print(f'错误信息: {response.text}')
            return False
            
    except requests.exceptions.Timeout:
        print(f'\n⏰ 请求超时')
        return False
    except requests.exceptions.RequestException as e:
        print(f'\n❌ 请求异常: {e}')
        return False
    except Exception as e:
        print(f'\n❌ 处理异常: {e}')
        return False

def main():
    """主函数"""
    # 检查商品00588793在zipcode 226553的配送情况
    zipcode = '226553'
    item_id = '00588793'
    
    success = check_delivery_with_item(zipcode, item_id)
    
    if success:
        print(f'\n🎉 检查完成!')
    else:
        print(f'\n❌ 检查失败!')

if __name__ == '__main__':
    main()
