#!/usr/bin/env python3
"""
Check delivery for a single item using existing delivery_test.py functions
"""

import sys
import os
from delivery_test import DeliveryTester
import json

def check_item_delivery(zipcode, item_id):
    """Check delivery for a specific item and zipcode"""
    
    print(f'🔍 检查配送情况')
    print(f'商品ID: {item_id}')
    print(f'邮编: {zipcode}')
    print('=' * 50)
    
    # Create a DeliveryTester instance
    tester = DeliveryTester()
    
    # Get access token
    try:
        token = tester.get_access_token()
        print('✅ 访问令牌获取成功')
    except Exception as e:
        print(f'❌ 获取访问令牌失败: {e}')
        return False
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    # Prepare payload with specific item
    payload = {
        "checkCapacity": True,
        "checkInventory": True,
        "businessUnit": {
            "type": "STO",
            "code": "1228"
        },
        "channelReferences": {
            "sellingChannelName": "DeliveryChecker"
        },
        "shipToAddress": {
            "country": "CN",
            "zipCode": str(zipcode)
        },
        "itemLines": {
            "itemLine": [
                {
                    "itemType": "ART",
                    "itemNo": str(item_id),
                    "id": "1",
                    "requiredQty": 1
                }
            ]
        },
        "serviceTypes": {
            "serviceType": [
                {
                    "id": "HOME_DELIVERY"
                }
            ]
        },
        "useLeadTimeOrchestration": True,
        "checkNoStock": True
    }
    
    print(f'正在调用配送API...')
    print(f'请求数据: {json.dumps(payload, indent=2)}')
    
    try:
        import requests
        # Use the same API call method as delivery_test.py
        response = requests.post(tester.api_url, headers=headers, json=payload, timeout=30, verify=False)
        
        print(f'\n📊 API响应结果:')
        print(f'状态码: {response.status_code}')
        print(f'响应时间: {response.elapsed.total_seconds():.3f}秒')
        
        if response.status_code == 200:
            result = response.json()
            print(f'\n✅ 请求成功!')
            
            # Parse delivery information
            if 'serviceTypes' in result and result['serviceTypes']:
                service_types = result['serviceTypes']
                if 'serviceType' in service_types and service_types['serviceType']:
                    print(f'\n🚚 配送服务详情:')
                    
                    services = service_types['serviceType']
                    if not isinstance(services, list):
                        services = [services]
                    
                    for i, service in enumerate(services, 1):
                        service_name = service.get('serviceTypeName', '未知服务')
                        print(f'\n服务 {i}: {service_name}')
                        
                        if 'solutions' in service and service['solutions']:
                            solutions = service['solutions']['solution']
                            if not isinstance(solutions, list):
                                solutions = [solutions]
                            
                            print(f'  配送方案数量: {len(solutions)}')
                            
                            for j, solution in enumerate(solutions, 1):
                                print(f'\n  方案 {j}:')
                                if 'deliveryLines' in solution:
                                    delivery_lines = solution['deliveryLines']['deliveryLine']
                                    if not isinstance(delivery_lines, list):
                                        delivery_lines = [delivery_lines]
                                    
                                    for k, line in enumerate(delivery_lines, 1):
                                        ship_node = line.get('shipNode', '未知')
                                        merge_node = line.get('mergeNode', '未知')
                                        transport = line.get('transportationMode', '未知')
                                        print(f'    配送线路 {k}: 发货节点={ship_node}, 合并节点={merge_node}, 运输方式={transport}')
                        else:
                            print(f'  ❌ 该服务暂无可用配送方案')
                else:
                    print(f'\n❌ 该地区暂无配送服务')
            else:
                print(f'\n❌ 响应中未找到配送服务信息')
            
            print(f'\n📋 完整响应数据:')
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            return True
                
        else:
            print(f'\n❌ 请求失败!')
            print(f'错误信息: {response.text}')
            return False
            
    except Exception as e:
        print(f'\n❌ 请求异常: {e}')
        return False

def main():
    """主函数"""
    # 检查商品00588793在zipcode 226553的配送情况
    zipcode = '226553'
    item_id = '00588793'

    print(f'测试商品ID: {item_id} 在邮编: {zipcode}\n')
    
    success = check_item_delivery(zipcode, item_id)
    
    if success:
        print(f'\n🎉 检查完成!')
    else:
        print(f'\n❌ 检查失败!')

if __name__ == '__main__':
    main()
