#!/usr/bin/env python3
"""
Check for zipcode issues with leading zeros
"""

import pandas as pd
import os
from dotenv import load_dotenv

load_dotenv()

def check_zipcode_issues():
    """Check for zipcode issues in input and output files"""
    
    print('🔍 ZipCode前导0问题检查')
    print('=' * 50)
    
    # 检查输入文件
    input_file = os.getenv('INPUT_EXCEL_FILE', 'masterfile-0805.xlsx')
    print(f'📁 输入文件: {input_file}')
    
    try:
        # 使用dtype=str来保持前导0
        df_input = pd.read_excel(input_file, dtype={'ZipCode': str})
        print(f'✅ 输入文件读取成功: {len(df_input)}行')
        
        # 分析zipcode长度
        zipcode_lengths = df_input['ZipCode'].str.len()
        length_counts = zipcode_lengths.value_counts().sort_index()
        
        print(f'\n📊 输入文件ZipCode长度分布:')
        for length, count in length_counts.items():
            print(f'  长度{length}: {count}个')
        
        # 查找以0开头的zipcode
        zero_start_mask = df_input['ZipCode'].str.startswith('0')
        zero_start_count = zero_start_mask.sum()
        
        print(f'\n🔍 以0开头的zipcode: {zero_start_count}个')
        
        if zero_start_count > 0:
            print('前10个以0开头的zipcode:')
            zero_start_zipcodes = df_input[zero_start_mask].head(10)
            for idx, row in zero_start_zipcodes.iterrows():
                print(f'  行{idx}: {row["ZipCode"]} (长度{len(row["ZipCode"])})')
        
    except Exception as e:
        print(f'❌ 读取输入文件失败: {e}')
        return
    
    # 检查最新的输出文件
    output_dir = os.getenv('OUTPUT_DIR', 'results')
    if os.path.exists(output_dir):
        test_dirs = sorted([d for d in os.listdir(output_dir) if d.startswith('test_')])
        if test_dirs:
            latest_dir = test_dirs[-1]
            results_path = os.path.join(output_dir, latest_dir)
            excel_files = [f for f in os.listdir(results_path) if f.endswith('.xlsx')]
            
            if excel_files:
                output_file = os.path.join(results_path, excel_files[0])
                print(f'\n📁 输出文件: {output_file}')
                
                try:
                    df_output = pd.read_excel(output_file)
                    print(f'✅ 输出文件读取成功: {len(df_output)}行')
                    
                    # 分析输出文件中的zipcode
                    output_zipcode_strings = df_output['ZipCode'].astype(str)
                    output_length_counts = output_zipcode_strings.str.len().value_counts().sort_index()
                    
                    print(f'\n📊 输出文件ZipCode长度分布:')
                    for length, count in output_length_counts.items():
                        print(f'  长度{length}: {count}个')
                    
                    # 比较输入和输出
                    print(f'\n⚖️ 输入vs输出对比:')
                    for length in sorted(set(list(length_counts.keys()) + list(output_length_counts.keys()))):
                        input_count = length_counts.get(length, 0)
                        output_count = output_length_counts.get(length, 0)
                        diff = output_count - input_count
                        status = '✅' if diff == 0 else '⚠️'
                        print(f'  长度{length}: 输入{input_count} -> 输出{output_count} ({diff:+d}) {status}')
                    
                    # 查找可能丢失前导0的zipcode
                    if zero_start_count > 0:
                        print(f'\n🚨 前导0丢失分析:')
                        print(f'  输入文件中以0开头的zipcode: {zero_start_count}个')
                        
                        # 检查输出文件中是否有对应的短zipcode
                        short_zipcodes = df_output[output_zipcode_strings.str.len() < 6]
                        print(f'  输出文件中长度<6的zipcode: {len(short_zipcodes)}个')
                        
                        if len(short_zipcodes) > 0:
                            print('  可能丢失前导0的zipcode示例:')
                            for i, (idx, row) in enumerate(short_zipcodes.head(10).iterrows()):
                                zipcode = row['ZipCode']
                                success = row.get('test_success', 'N/A')
                                print(f'    行{idx}: {zipcode} -> 应该是0{zipcode}? (测试成功: {success})')
                
                except Exception as e:
                    print(f'❌ 读取输出文件失败: {e}')
    
    # 提供修复建议
    if zero_start_count > 0:
        print(f'\n💡 修复建议:')
        print('1. 修改脚本读取Excel时指定dtype={"ZipCode": str}')
        print('2. 重新运行受影响的zipcode测试')
        print('3. 确保API调用时zipcode保持字符串格式')
        
        return True  # 需要修复
    else:
        print(f'\n✅ 没有发现前导0问题')
        return False  # 不需要修复

if __name__ == '__main__':
    needs_fix = check_zipcode_issues()
    if needs_fix:
        print(f'\n🔧 需要修复前导0问题')
    else:
        print(f'\n✅ ZipCode格式正确')
