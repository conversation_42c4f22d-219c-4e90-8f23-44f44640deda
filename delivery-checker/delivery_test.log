2025-08-05 12:10:11,736 - INFO - Starting delivery arrangement test...
2025-08-05 12:10:23,494 - INFO - Loaded Excel file with 30898 rows
2025-08-05 12:10:23,494 - INFO - Processing rows 0 to 19 (total: 20 rows)
2025-08-05 12:10:23,498 - INFO - Processing batch: rows 0-4 (5 zipcodes)
2025-08-05 12:10:23,498 - INFO - Requesting new access token...
2025-08-05 12:10:24,164 - INFO - Access token obtained, expires at: 2025-08-05 13:10:23.164805
2025-08-05 12:10:24,210 - INFO - Row 1: 409911 - FAILED (Nones)
2025-08-05 12:10:24,210 - INFO - Row 3: 404101 - FAILED (Nones)
2025-08-05 12:10:24,210 - INFO - Row 4: 409600 - FAILED (Nones)
2025-08-05 12:10:24,210 - INFO - Row 0: 409809 - FAILED (Nones)
2025-08-05 12:10:24,211 - INFO - Row 2: 409108 - FAILED (Nones)
2025-08-05 12:10:38,708 - INFO - Updated Excel file with batch results
2025-08-05 12:10:39,711 - INFO - Processing batch: rows 5-9 (5 zipcodes)
2025-08-05 12:10:39,741 - INFO - Row 5: 405899 - FAILED (Nones)
2025-08-05 12:10:39,748 - INFO - Row 7: 404600 - FAILED (Nones)
2025-08-05 12:10:39,751 - INFO - Row 9: 404500 - FAILED (Nones)
2025-08-05 12:10:39,751 - INFO - Row 8: 405900 - FAILED (Nones)
2025-08-05 12:10:39,751 - INFO - Row 6: 404700 - FAILED (Nones)
2025-08-05 12:10:54,195 - INFO - Updated Excel file with batch results
2025-08-05 12:10:55,200 - INFO - Processing batch: rows 10-14 (5 zipcodes)
2025-08-05 12:10:55,227 - INFO - Row 10: 666315 - FAILED (Nones)
2025-08-05 12:10:55,228 - INFO - Row 14: 666313 - FAILED (Nones)
2025-08-05 12:10:55,228 - INFO - Row 13: 666305 - FAILED (Nones)
2025-08-05 12:10:55,228 - INFO - Row 11: 666310 - FAILED (Nones)
2025-08-05 12:10:55,228 - INFO - Row 12: 666307 - FAILED (Nones)
2025-08-05 12:10:58,027 - INFO - Test interrupted by user
2025-08-05 12:14:51,175 - INFO - Starting delivery arrangement test...
2025-08-05 12:14:51,278 - INFO - Loaded Excel file with 20 rows
2025-08-05 12:14:51,278 - INFO - Processing rows 0 to 4 (total: 5 rows)
2025-08-05 12:14:51,279 - INFO - Processing batch: rows 0-1 (2 zipcodes)
2025-08-05 12:14:51,279 - INFO - Requesting new access token...
2025-08-05 12:14:52,632 - INFO - Access token obtained, expires at: 2025-08-05 13:14:51.632135
2025-08-05 12:14:52,675 - ERROR - Row 0: 409809 - FAILED (Nones) - Error: SSLError: HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:52,675 - ERROR -   Exception: SSLError - HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:52,675 - ERROR - Row 1: 409911 - FAILED (Nones) - Error: SSLError: HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:52,675 - ERROR -   Exception: SSLError - HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:52,694 - INFO - Updated Excel file with batch results
2025-08-05 12:14:53,697 - INFO - Processing batch: rows 2-3 (2 zipcodes)
2025-08-05 12:14:53,734 - ERROR - Row 3: 404101 - FAILED (Nones) - Error: SSLError: HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:53,735 - ERROR -   Exception: SSLError - HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:53,735 - ERROR - Row 2: 409108 - FAILED (Nones) - Error: SSLError: HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:53,735 - ERROR -   Exception: SSLError - HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:53,751 - INFO - Updated Excel file with batch results
2025-08-05 12:14:54,760 - INFO - Processing batch: rows 4-4 (1 zipcodes)
2025-08-05 12:14:54,796 - ERROR - Row 4: 409600 - FAILED (Nones) - Error: SSLError: HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:54,796 - ERROR -   Exception: SSLError - HTTPSConnectionPool(host='private-api.ingka.prodcn.ikea.com', port=443): Max retries exceeded with url: /cfb/customer-promise/cn/delivery-arrangement (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self-signed certificate in certificate chain (_ssl.c:1006)')))
2025-08-05 12:14:54,812 - INFO - Updated Excel file with batch results
2025-08-05 12:14:54,812 - INFO - Test completed!
2025-08-05 12:14:54,813 - INFO - Total processed: 20
2025-08-05 12:14:54,813 - INFO - Successful: 0
2025-08-05 12:14:54,813 - INFO - Failed: 20
2025-08-05 12:14:54,813 - INFO - Cache hits: 5
2025-08-05 12:16:59,833 - INFO - Resetting progress...
2025-08-05 12:16:59,834 - INFO - Starting delivery arrangement test...
2025-08-05 12:16:59,974 - INFO - Loaded Excel file with 20 rows
2025-08-05 12:16:59,974 - INFO - Processing rows 0 to 4 (total: 5 rows)
2025-08-05 12:16:59,976 - INFO - Processing batch: rows 0-1 (2 zipcodes)
2025-08-05 12:16:59,976 - INFO - Requesting new access token...
2025-08-05 12:17:00,652 - INFO - Access token obtained, expires at: 2025-08-05 13:16:59.651903
2025-08-05 12:17:00,742 - INFO - Row 0: 409809 - SUCCESS (0.08491s) - Delivery options: 0, Available: False
2025-08-05 12:17:00,757 - INFO - Row 1: 409911 - SUCCESS (0.100267s) - Delivery options: 0, Available: False
2025-08-05 12:17:00,779 - INFO - Updated Excel file with batch results
2025-08-05 12:17:01,783 - INFO - Processing batch: rows 2-3 (2 zipcodes)
2025-08-05 12:17:01,875 - INFO - Row 3: 404101 - SUCCESS (0.084301s) - Delivery options: 0, Available: False
2025-08-05 12:17:01,877 - INFO - Row 2: 409108 - SUCCESS (0.087215s) - Delivery options: 0, Available: False
2025-08-05 12:17:01,891 - INFO - Updated Excel file with batch results
2025-08-05 12:17:02,893 - INFO - Processing batch: rows 4-4 (1 zipcodes)
2025-08-05 12:17:02,975 - INFO - Row 4: 409600 - SUCCESS (0.077204s) - Delivery options: 0, Available: False
2025-08-05 12:17:02,992 - INFO - Updated Excel file with batch results
2025-08-05 12:17:02,992 - INFO - Test completed!
2025-08-05 12:17:02,992 - INFO - Total processed: 5
2025-08-05 12:17:02,992 - INFO - Successful: 5
2025-08-05 12:17:02,992 - INFO - Failed: 0
2025-08-05 12:17:02,992 - INFO - Cache hits: 5
2025-08-05 12:17:11,127 - INFO - Resetting progress...
2025-08-05 12:17:11,127 - INFO - Starting delivery arrangement test...
2025-08-05 12:17:11,243 - INFO - Loaded Excel file with 20 rows
2025-08-05 12:17:11,245 - INFO - Processing rows 0 to 19 (total: 20 rows)
2025-08-05 12:17:11,245 - INFO - Processing batch: rows 0-4 (5 zipcodes)
2025-08-05 12:17:11,245 - INFO - Requesting new access token...
2025-08-05 12:17:11,870 - INFO - Access token obtained, expires at: 2025-08-05 13:17:10.870601
2025-08-05 12:17:11,963 - INFO - Row 2: 409108 - SUCCESS (0.084905s) - Delivery options: 0, Available: False
2025-08-05 12:17:11,964 - INFO - Row 0: 409809 - SUCCESS (0.085672s) - Delivery options: 0, Available: False
2025-08-05 12:17:11,967 - INFO - Row 1: 409911 - SUCCESS (0.090847s) - Delivery options: 0, Available: False
2025-08-05 12:17:11,969 - INFO - Row 4: 409600 - SUCCESS (0.087917s) - Delivery options: 0, Available: False
2025-08-05 12:17:11,986 - INFO - Row 3: 404101 - SUCCESS (0.105583s) - Delivery options: 0, Available: False
2025-08-05 12:17:12,006 - INFO - Updated Excel file with batch results
2025-08-05 12:17:13,013 - INFO - Processing batch: rows 5-9 (5 zipcodes)
2025-08-05 12:17:13,099 - INFO - Row 5: 400000 - SUCCESS (0.082808s) - Delivery options: 0, Available: False
2025-08-05 12:17:13,157 - INFO - Row 8: 400003 - SUCCESS (0.133566s) - Delivery options: 0, Available: False
2025-08-05 12:17:13,158 - INFO - Row 9: 400004 - SUCCESS (0.13108s) - Delivery options: 0, Available: False
2025-08-05 12:17:13,158 - INFO - Row 6: 400001 - SUCCESS (0.139387s) - Delivery options: 0, Available: False
2025-08-05 12:17:13,158 - INFO - Row 7: 400002 - SUCCESS (0.138642s) - Delivery options: 0, Available: False
2025-08-05 12:17:13,169 - INFO - Updated Excel file with batch results
2025-08-05 12:17:14,173 - INFO - Processing batch: rows 10-14 (5 zipcodes)
2025-08-05 12:17:14,272 - INFO - Row 13: 200003 - SUCCESS (0.0868s) - Delivery options: 0, Available: False
2025-08-05 12:17:14,278 - INFO - Row 10: 200000 - SUCCESS (0.092786s) - Delivery options: 0, Available: False
2025-08-05 12:17:14,280 - INFO - Row 11: 200001 - SUCCESS (0.094543s) - Delivery options: 0, Available: False
2025-08-05 12:17:14,280 - INFO - Row 14: 200004 - SUCCESS (0.092635s) - Delivery options: 0, Available: False
2025-08-05 12:17:14,280 - INFO - Row 12: 200002 - SUCCESS (0.100059s) - Delivery options: 0, Available: False
2025-08-05 12:17:14,295 - INFO - Updated Excel file with batch results
2025-08-05 12:17:15,304 - INFO - Processing batch: rows 15-19 (5 zipcodes)
2025-08-05 12:17:15,386 - INFO - Row 17: 200007 - SUCCESS (0.074102s) - Delivery options: 0, Available: False
2025-08-05 12:17:15,386 - INFO - Row 16: 200006 - SUCCESS (0.076475s) - Delivery options: 0, Available: False
2025-08-05 12:17:15,390 - INFO - Row 19: 200009 - SUCCESS (0.076915s) - Delivery options: 0, Available: False
2025-08-05 12:17:15,390 - INFO - Row 18: 200008 - SUCCESS (0.078209s) - Delivery options: 0, Available: False
2025-08-05 12:17:15,396 - INFO - Row 15: 200005 - SUCCESS (0.088883s) - Delivery options: 0, Available: False
2025-08-05 12:17:15,409 - INFO - Updated Excel file with batch results
2025-08-05 12:17:15,409 - INFO - Test completed!
2025-08-05 12:17:15,409 - INFO - Total processed: 20
2025-08-05 12:17:15,409 - INFO - Successful: 20
2025-08-05 12:17:15,409 - INFO - Failed: 0
2025-08-05 12:17:15,409 - INFO - Cache hits: 20
2025-08-05 13:12:45,832 - INFO - Resetting progress...
2025-08-05 13:12:45,833 - INFO - Starting delivery arrangement test...
2025-08-05 13:12:45,982 - INFO - Loaded Excel file with 20 rows
2025-08-05 13:12:45,982 - INFO - Processing rows 0 to 4 (total: 5 rows)
2025-08-05 13:12:45,983 - INFO - Processing batch: rows 0-1 (2 zipcodes)
2025-08-05 13:12:45,983 - INFO - Requesting new access token...
2025-08-05 13:12:46,732 - INFO - Access token obtained, expires at: 2025-08-05 14:12:45.732826
2025-08-05 13:12:46,821 - INFO - Row 0: 409809 - SUCCESS (0.083987s) - Delivery options: 0, Available: False
2025-08-05 13:12:46,826 - INFO - Row 1: 409911 - SUCCESS (0.087599s) - Delivery options: 0, Available: False
2025-08-05 13:12:46,848 - INFO - Updated Excel file with batch results
2025-08-05 13:12:47,853 - INFO - Processing batch: rows 2-3 (2 zipcodes)
2025-08-05 13:12:47,939 - INFO - Row 2: 409108 - SUCCESS (0.081778s) - Delivery options: 0, Available: False
2025-08-05 13:12:47,958 - INFO - Row 3: 404101 - SUCCESS (0.100004s) - Delivery options: 0, Available: False
2025-08-05 13:12:47,970 - INFO - Updated Excel file with batch results
2025-08-05 13:12:48,978 - INFO - Processing batch: rows 4-4 (1 zipcodes)
2025-08-05 13:12:49,068 - INFO - Row 4: 409600 - SUCCESS (0.086457s) - Delivery options: 0, Available: False
2025-08-05 13:12:49,080 - INFO - Updated Excel file with batch results
2025-08-05 13:12:49,081 - INFO - Saved 5 detailed responses to api_responses.json
2025-08-05 13:12:49,081 - INFO - Test completed!
2025-08-05 13:12:49,081 - INFO - Total processed: 5
2025-08-05 13:12:49,081 - INFO - Successful: 5
2025-08-05 13:12:49,081 - INFO - Failed: 0
2025-08-05 13:12:49,081 - INFO - Cache hits: 5
2025-08-05 13:12:49,081 - INFO - Detailed responses saved to: api_responses.json
2025-08-05 13:14:33,616 - INFO - Resetting progress...
2025-08-05 13:14:33,617 - INFO - Starting delivery arrangement test...
2025-08-05 13:14:33,722 - INFO - Loaded Excel file with 20 rows
2025-08-05 13:14:33,722 - INFO - Processing rows 0 to 4 (total: 5 rows)
2025-08-05 13:14:33,723 - INFO - Processing batch: rows 0-1 (2 zipcodes)
2025-08-05 13:14:33,723 - INFO - Requesting new access token...
2025-08-05 13:14:34,412 - INFO - Access token obtained, expires at: 2025-08-05 14:14:33.412692
2025-08-05 13:14:34,913 - INFO - Row 1: 409911 - SUCCESS (0.496426s) - Delivery options: 0, Available: False
2025-08-05 13:14:34,972 - INFO - Row 0: 409809 - SUCCESS (0.55559s) - Delivery options: 0, Available: False
2025-08-05 13:14:34,988 - INFO - Updated Excel file with batch results
2025-08-05 13:14:35,993 - INFO - Processing batch: rows 2-3 (2 zipcodes)
2025-08-05 13:14:36,438 - INFO - Row 2: 409108 - SUCCESS (0.435839s) - Delivery options: 0, Available: False
2025-08-05 13:14:36,438 - INFO - Row 3: 404101 - SUCCESS (0.435567s) - Delivery options: 0, Available: False
2025-08-05 13:14:36,456 - INFO - Updated Excel file with batch results
2025-08-05 13:14:37,457 - INFO - Processing batch: rows 4-4 (1 zipcodes)
2025-08-05 13:14:37,916 - INFO - Row 4: 409600 - SUCCESS (0.454772s) - Delivery options: 0, Available: False
2025-08-05 13:14:37,926 - INFO - Updated Excel file with batch results
2025-08-05 13:14:37,927 - INFO - Saved 5 detailed responses to api_responses.json
2025-08-05 13:14:37,927 - INFO - Test completed!
2025-08-05 13:14:37,927 - INFO - Total processed: 5
2025-08-05 13:14:37,927 - INFO - Successful: 5
2025-08-05 13:14:37,927 - INFO - Failed: 0
2025-08-05 13:14:37,927 - INFO - Cache hits: 5
2025-08-05 13:14:37,927 - INFO - Detailed responses saved to: api_responses.json
2025-08-05 13:18:20,845 - INFO - Resetting progress...
2025-08-05 13:18:20,846 - INFO - Starting delivery arrangement test...
2025-08-05 13:18:20,958 - INFO - Loaded Excel file with 20 rows
2025-08-05 13:18:20,958 - INFO - Processing rows 0 to 2 (total: 3 rows)
2025-08-05 13:18:20,959 - INFO - Processing batch: rows 0-0 (1 zipcodes)
2025-08-05 13:18:20,960 - INFO - Requesting new access token...
2025-08-05 13:18:21,647 - INFO - Access token obtained, expires at: 2025-08-05 14:18:20.646971
2025-08-05 13:18:22,049 - INFO - Row 0: 409809 - SUCCESS (0.39904s) - Delivery options: 1, Available: True
2025-08-05 13:18:22,049 - INFO -   Service 1: HOME_DELIVERY - Solutions: 1
2025-08-05 13:18:22,049 - INFO -   Delivery Line 1: ShipNode: CDC.037, MergeNode: LSC.1313, MergeNodeList: LSC.1313, Transport: PARCEL
2025-08-05 13:18:22,068 - INFO - Updated Excel file with batch results
2025-08-05 13:18:23,074 - INFO - Processing batch: rows 1-1 (1 zipcodes)
2025-08-05 13:18:23,549 - INFO - Row 1: 409911 - SUCCESS (0.469407s) - Delivery options: 1, Available: True
2025-08-05 13:18:23,550 - INFO -   Service 1: HOME_DELIVERY - Solutions: 1
2025-08-05 13:18:23,550 - INFO -   Delivery Line 1: ShipNode: CDC.037, MergeNode: LSC.1313, MergeNodeList: LSC.1313, Transport: PARCEL
2025-08-05 13:18:23,568 - INFO - Updated Excel file with batch results
2025-08-05 13:18:24,571 - INFO - Processing batch: rows 2-2 (1 zipcodes)
2025-08-05 13:18:25,028 - INFO - Row 2: 409108 - SUCCESS (0.449537s) - Delivery options: 1, Available: True
2025-08-05 13:18:25,028 - INFO -   Service 1: HOME_DELIVERY - Solutions: 1
2025-08-05 13:18:25,029 - INFO -   Delivery Line 1: ShipNode: CDC.037, MergeNode: LSC.1313, MergeNodeList: LSC.1313, Transport: PARCEL
2025-08-05 13:18:25,046 - INFO - Updated Excel file with batch results
2025-08-05 13:18:25,049 - INFO - Saved 3 detailed responses to api_responses.json
2025-08-05 13:18:25,049 - INFO - Test completed!
2025-08-05 13:18:25,049 - INFO - Total processed: 3
2025-08-05 13:18:25,049 - INFO - Successful: 3
2025-08-05 13:18:25,049 - INFO - Failed: 0
2025-08-05 13:18:25,049 - INFO - Cache hits: 3
2025-08-05 13:18:25,049 - INFO - Detailed responses saved to: api_responses.json
2025-08-05 13:22:27,991 - INFO - Input file: masterfile-0805.xlsx
2025-08-05 13:22:27,992 - INFO - Output file: delivery_test_results.xlsx
2025-08-05 13:22:27,992 - INFO - Progress file: progress.json
2025-08-05 13:22:27,992 - INFO - Resetting progress...
2025-08-05 13:22:27,992 - INFO - Starting delivery arrangement test...
2025-08-05 13:22:28,128 - INFO - Loaded input Excel file with 20 rows
2025-08-05 13:22:28,128 - INFO - Creating new output file from input
2025-08-05 13:22:28,128 - INFO - Processing rows 0 to 2 (total: 3 rows)
2025-08-05 13:22:28,129 - INFO - Processing batch: rows 0-0 (1 zipcodes)
2025-08-05 13:22:28,129 - INFO - Requesting new access token...
2025-08-05 13:22:28,770 - INFO - Access token obtained, expires at: 2025-08-05 14:22:27.770139
2025-08-05 13:22:29,208 - INFO - Row 0: 409809 - SUCCESS (0.432595s) - Delivery options: 1, Available: True
2025-08-05 13:22:29,208 - INFO -   Service 1: HOME_DELIVERY - Solutions: 1
2025-08-05 13:22:29,208 - INFO -   Delivery Line 1: ShipNode: CDC.037, MergeNode: LSC.1313, MergeNodeList: LSC.1313, Transport: PARCEL
2025-08-05 13:22:29,232 - INFO - Updated output Excel file with batch results: delivery_test_results.xlsx
2025-08-05 13:22:30,238 - INFO - Processing batch: rows 1-1 (1 zipcodes)
2025-08-05 13:22:30,716 - INFO - Row 1: 409911 - SUCCESS (0.472128s) - Delivery options: 1, Available: True
2025-08-05 13:22:30,716 - INFO -   Service 1: HOME_DELIVERY - Solutions: 1
2025-08-05 13:22:30,716 - INFO -   Delivery Line 1: ShipNode: CDC.037, MergeNode: LSC.1313, MergeNodeList: LSC.1313, Transport: PARCEL
2025-08-05 13:22:30,730 - INFO - Updated output Excel file with batch results: delivery_test_results.xlsx
2025-08-05 13:22:31,737 - INFO - Processing batch: rows 2-2 (1 zipcodes)
2025-08-05 13:22:32,097 - INFO - Row 2: 409108 - SUCCESS (0.355561s) - Delivery options: 1, Available: True
2025-08-05 13:22:32,098 - INFO -   Service 1: HOME_DELIVERY - Solutions: 1
2025-08-05 13:22:32,098 - INFO -   Delivery Line 1: ShipNode: CDC.037, MergeNode: LSC.1313, MergeNodeList: LSC.1313, Transport: PARCEL
2025-08-05 13:22:32,111 - INFO - Updated output Excel file with batch results: delivery_test_results.xlsx
2025-08-05 13:22:32,113 - INFO - Saved 3 detailed responses to api_responses.json
2025-08-05 13:22:32,114 - INFO - Test completed!
2025-08-05 13:22:32,114 - INFO - Total processed: 3
2025-08-05 13:22:32,114 - INFO - Successful: 3
2025-08-05 13:22:32,114 - INFO - Failed: 0
2025-08-05 13:22:32,114 - INFO - Cache hits: 3
2025-08-05 13:22:32,114 - INFO - Detailed responses saved to: api_responses.json
