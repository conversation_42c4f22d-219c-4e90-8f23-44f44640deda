#!/usr/bin/env python3
"""
Delivery Arrangement API Testing Script

This script tests the delivery-arrangement API using zipcodes from an Excel file.
It includes OAuth token management, concurrent processing, progress tracking, and result caching.
"""

import os
import sys
import json
import time
import logging
import argparse
import requests
import pandas as pd
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from dotenv import load_dotenv
from pathlib import Path
import urllib3

# Disable SSL warnings for internal APIs
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('delivery_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DeliveryTester:
    def __init__(self):
        # Configuration from environment
        self.client_id = os.getenv('CLIENT_ID')
        self.client_secret = os.getenv('CLIENT_SECRET')
        self.concurrency = int(os.getenv('CONCURRENCY', '5'))
        self.input_excel_file = os.getenv('INPUT_EXCEL_FILE', 'masterfile-0805.xlsx')
        self.output_excel_file = os.getenv('OUTPUT_EXCEL_FILE', 'delivery_test_results.xlsx')
        self.progress_file = os.getenv('PROGRESS_FILE', 'progress.json')
        
        # API endpoints
        self.token_url = "https://login.microsoftonline.com/720b637a-655a-40cf-816a-f22f40755c2c/oauth2/v2.0/token"
        self.api_url = "https://private-api.ingka.prodcn.ikea.com/cfb/customer-promise/cn/delivery-arrangement"
        
        # Token management
        self.token = None
        self.token_expires_at = None
        self.token_lock = Lock()
        
        # Progress tracking
        self.progress = self.load_progress()
        self.progress_lock = Lock()
        
        # Result caching for current session
        self.result_cache = {}
        self.cache_lock = Lock()

        # Detailed response logging
        self.detailed_responses = []
        self.response_log_file = os.getenv('RESPONSE_LOG_FILE', 'api_responses.json')
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self):
        """Validate required configuration"""
        if not self.client_id or not self.client_secret:
            raise ValueError("CLIENT_ID and CLIENT_SECRET must be set in .env file")

        if not os.path.exists(self.input_excel_file):
            raise FileNotFoundError(f"Input Excel file not found: {self.input_excel_file}")

        # Log configuration
        logger.info(f"Input file: {self.input_excel_file}")
        logger.info(f"Output file: {self.output_excel_file}")
        logger.info(f"Progress file: {self.progress_file}")
    
    def load_progress(self):
        """Load progress from file"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load progress file: {e}")
        
        return {
            'last_processed_row': 0,
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'start_time': None,
            'last_update': None
        }
    
    def save_progress(self):
        """Save progress to file"""
        with self.progress_lock:
            self.progress['last_update'] = datetime.now().isoformat()
            try:
                with open(self.progress_file, 'w') as f:
                    json.dump(self.progress, f, indent=2)
            except Exception as e:
                logger.error(f"Failed to save progress: {e}")
    
    def get_access_token(self):
        """Get or refresh OAuth access token"""
        with self.token_lock:
            # Check if token is still valid (with 5 minute buffer)
            if (self.token and self.token_expires_at and 
                datetime.now() < self.token_expires_at - timedelta(minutes=5)):
                return self.token
            
            logger.info("Requesting new access token...")
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'scope': 'https://api.prod.cn.ingka.com/.default',
                'grant_type': 'client_credentials',
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }
            
            try:
                response = requests.post(self.token_url, headers=headers, data=data, timeout=30)
                response.raise_for_status()
                
                token_data = response.json()
                self.token = token_data['access_token']
                expires_in = token_data.get('expires_in', 3600)  # Default 1 hour
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                logger.info(f"Access token obtained, expires at: {self.token_expires_at}")
                return self.token
                
            except Exception as e:
                logger.error(f"Failed to get access token: {e}")
                raise
    
    def test_delivery_arrangement(self, zipcode):
        """Test delivery arrangement for a specific zipcode"""
        # Check cache first
        with self.cache_lock:
            if zipcode in self.result_cache:
                logger.debug(f"Using cached result for zipcode: {zipcode}")
                return self.result_cache[zipcode]
        
        token = self.get_access_token()
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }
        
        payload = {
            "checkCapacity": True,
            "checkInventory": True,
            "businessUnit": {
                "type": "STO",
                "code": "1228"
            },
            "channelReferences": {
                "sellingChannelName": "DeliveryChecker"
            },
            "shipToAddress": {
                "country": "CN",
                "zipCode": str(zipcode)
            },
            "itemLines": {
                "itemLine": [
                    {
                        "itemType": "ART",
                        "itemNo": "10534224",
                        "id": "1",
                        "requiredQty": 1
                    }
                ]
            },
            "serviceTypes": {
                "serviceType": [
                    {
                        "id": "HOME_DELIVERY"
                    }
                ]
            },
            "useLeadTimeOrchestration": True,
            "checkNoStock": True
        }
        
        try:
            # Disable SSL verification for internal APIs with self-signed certificates
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=30, verify=False)
            
            result = {
                'zipcode': zipcode,
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'response_time': response.elapsed.total_seconds(),
                'timestamp': datetime.now().isoformat(),
                'request_headers': dict(response.request.headers),
                'response_headers': dict(response.headers)
            }

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    result['response_data'] = response_data
                    result['error'] = None

                    # Extract useful information from successful response
                    if 'serviceTypes' in response_data and 'serviceType' in response_data['serviceTypes']:
                        service_types = response_data['serviceTypes']['serviceType']
                        result['delivery_services_count'] = len(service_types)
                        result['has_delivery_options'] = len(service_types) > 0

                        # Extract delivery service details
                        service_details = []
                        delivery_lines_info = []

                        for service in service_types:
                            service_info = {
                                'service_id': service.get('id', 'Unknown'),
                                'possible_solutions': len(service.get('possibleSolutions', {}).get('possibleSolution', []))
                            }

                            # Extract delivery line details (shipNode, mergeNode, etc.)
                            if 'possibleSolutions' in service:
                                solutions = service['possibleSolutions'].get('possibleSolution', [])
                                for solution in solutions:
                                    solution_info = {
                                        'solution_id': solution.get('id', 'Unknown'),
                                        'service': solution.get('service', 'Unknown'),
                                        'earliest_ship_date': solution.get('earliestShipDate', 'Unknown')
                                    }

                                    # Extract delivery lines with shipNode, mergeNode, etc.
                                    if 'deliveryLines' in solution:
                                        delivery_lines = solution['deliveryLines'].get('deliveryLine', [])
                                        for line in delivery_lines:
                                            line_info = {
                                                'delivery_id': line.get('deliveryId', 'Unknown'),
                                                'ship_node': line.get('shipNode', 'Unknown'),
                                                'merge_node': line.get('mergeNode', 'Unknown'),
                                                'merge_node_list': line.get('mergeNodeList', 'Unknown'),
                                                'transport_method': line.get('transportMethodType', 'Unknown'),
                                                'service_item_id': line.get('serviceItemId', 'Unknown'),
                                                'unit_of_measure': line.get('unitOfMeasure', 'Unknown')
                                            }
                                            delivery_lines_info.append(line_info)

                                    service_info['solutions'] = service_info.get('solutions', [])
                                    service_info['solutions'].append(solution_info)

                            service_details.append(service_info)

                        result['delivery_service_details'] = service_details
                        result['delivery_lines_info'] = delivery_lines_info
                    else:
                        result['delivery_services_count'] = 0
                        result['has_delivery_options'] = False
                        result['delivery_service_details'] = []
                        result['delivery_lines_info'] = []

                except Exception as json_error:
                    result['response_data'] = response.text
                    result['error'] = f"JSON parsing error: {str(json_error)}"
                    result['success'] = False
            else:
                # Capture detailed error information
                result['response_data'] = None
                error_details = {
                    'status_code': response.status_code,
                    'reason': response.reason,
                    'response_text': response.text,
                    'url': response.url,
                    'request_method': response.request.method
                }

                # Try to parse error response as JSON for more details
                try:
                    error_json = response.json()
                    error_details['error_json'] = error_json
                    if 'message' in error_json:
                        error_details['error_message'] = error_json['message']
                    if 'code' in error_json:
                        error_details['error_code'] = error_json['code']
                except:
                    error_details['error_json'] = None

                result['error'] = error_details
                result['error_summary'] = f"HTTP {response.status_code} {response.reason}: {response.text[:200]}..."
            
            # Cache the result
            with self.cache_lock:
                self.result_cache[zipcode] = result

            # Save detailed response for logging
            self.save_detailed_response(zipcode, result)

            return result
            
        except Exception as e:
            import traceback

            result = {
                'zipcode': zipcode,
                'status_code': None,
                'success': False,
                'response_time': None,
                'timestamp': datetime.now().isoformat(),
                'response_data': None,
                'error': {
                    'exception_type': type(e).__name__,
                    'exception_message': str(e),
                    'traceback': traceback.format_exc(),
                    'request_url': self.api_url,
                    'request_payload': payload
                },
                'error_summary': f"{type(e).__name__}: {str(e)}"
            }
            
            # Cache the error result too
            with self.cache_lock:
                self.result_cache[zipcode] = result
            
            return result

    def save_detailed_response(self, zipcode, result):
        """Save detailed API response to JSON log file"""
        try:
            detailed_entry = {
                'zipcode': zipcode,
                'timestamp': result.get('timestamp'),
                'success': result.get('success'),
                'status_code': result.get('status_code'),
                'response_time': result.get('response_time'),
                'response_data': result.get('response_data'),
                'error': result.get('error'),
                'request_headers': result.get('request_headers'),
                'response_headers': result.get('response_headers')
            }

            self.detailed_responses.append(detailed_entry)

            # Save to file periodically (every 10 responses) or if it's an error
            if len(self.detailed_responses) % 10 == 0 or not result.get('success'):
                self.flush_detailed_responses()

        except Exception as e:
            logger.warning(f"Failed to save detailed response for {zipcode}: {e}")

    def flush_detailed_responses(self):
        """Flush detailed responses to JSON file"""
        if not self.detailed_responses:
            return

        try:
            # Load existing responses if file exists
            existing_responses = []
            if os.path.exists(self.response_log_file):
                try:
                    with open(self.response_log_file, 'r') as f:
                        existing_responses = json.load(f)
                except:
                    existing_responses = []

            # Append new responses
            existing_responses.extend(self.detailed_responses)

            # Save back to file
            with open(self.response_log_file, 'w') as f:
                json.dump(existing_responses, f, indent=2, default=str)

            logger.info(f"Saved {len(self.detailed_responses)} detailed responses to {self.response_log_file}")
            self.detailed_responses = []

        except Exception as e:
            logger.error(f"Failed to flush detailed responses: {e}")

    def process_zipcode_batch(self, zipcodes, start_row):
        """Process a batch of zipcodes with concurrent execution"""
        results = []

        with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
            # Submit all tasks
            future_to_zipcode = {
                executor.submit(self.test_delivery_arrangement, zipcode): (zipcode, start_row + i)
                for i, zipcode in enumerate(zipcodes)
            }

            # Process completed tasks
            for future in as_completed(future_to_zipcode):
                zipcode, row_index = future_to_zipcode[future]

                try:
                    result = future.result()
                    results.append((row_index, result))

                    # Update progress
                    with self.progress_lock:
                        self.progress['total_processed'] += 1
                        if result['success']:
                            self.progress['successful'] += 1
                        else:
                            self.progress['failed'] += 1

                        self.progress['last_processed_row'] = max(
                            self.progress['last_processed_row'], row_index
                        )

                    # Log progress with detailed information
                    status = "SUCCESS" if result['success'] else "FAILED"
                    response_time = result.get('response_time', 'N/A')

                    if result['success']:
                        delivery_count = result.get('delivery_services_count', 'N/A')
                        has_options = result.get('has_delivery_options', False)
                        service_details = result.get('delivery_service_details', [])
                        delivery_lines_info = result.get('delivery_lines_info', [])

                        logger.info(f"Row {row_index}: {zipcode} - {status} "
                                  f"({response_time}s) - Delivery options: {delivery_count}, Available: {has_options}")

                        # Log detailed service information if available
                        if service_details:
                            for i, service in enumerate(service_details):
                                logger.info(f"  Service {i+1}: {service['service_id']} - "
                                          f"Solutions: {service['possible_solutions']}")

                        # Log delivery lines information (shipNode, mergeNode, etc.)
                        if delivery_lines_info:
                            for i, line in enumerate(delivery_lines_info):
                                logger.info(f"  Delivery Line {i+1}: "
                                          f"ShipNode: {line['ship_node']}, "
                                          f"MergeNode: {line['merge_node']}, "
                                          f"MergeNodeList: {line['merge_node_list']}, "
                                          f"Transport: {line['transport_method']}")

                        # Log full API response for successful calls (can be disabled for production)
                        if result.get('response_data'):
                            logger.debug(f"  Full API Response: {json.dumps(result['response_data'], indent=2)}")
                    else:
                        error_summary = result.get('error_summary', result.get('error', 'Unknown error'))
                        logger.error(f"Row {row_index}: {zipcode} - {status} "
                                   f"({response_time}s) - Error: {error_summary}")

                        # Log detailed error for debugging
                        if isinstance(result.get('error'), dict):
                            error_details = result['error']
                            if 'status_code' in error_details:
                                logger.error(f"  HTTP Status: {error_details['status_code']} {error_details.get('reason', '')}")
                            if 'error_message' in error_details:
                                logger.error(f"  API Error: {error_details['error_message']}")
                            if 'exception_type' in error_details:
                                logger.error(f"  Exception: {error_details['exception_type']} - {error_details['exception_message']}")
                            if 'response_text' in error_details and error_details['response_text']:
                                logger.error(f"  Response: {error_details['response_text'][:300]}...")

                except Exception as e:
                    logger.error(f"Error processing zipcode {zipcode}: {e}")
                    results.append((row_index, {
                        'zipcode': zipcode,
                        'success': False,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    }))

        return results

    def run_test(self, start_row=None, end_row=None, batch_size=50):
        """Run the delivery test for specified row range"""
        logger.info("Starting delivery arrangement test...")

        # Load input Excel file
        try:
            input_df = pd.read_excel(self.input_excel_file)
            logger.info(f"Loaded input Excel file with {len(input_df)} rows")
        except Exception as e:
            logger.error(f"Failed to load input Excel file: {e}")
            return False

        # Load existing output file if it exists, otherwise create from input
        try:
            if os.path.exists(self.output_excel_file):
                df = pd.read_excel(self.output_excel_file)
                logger.info(f"Loaded existing output file with {len(df)} rows")

                # Verify the output file has the same structure as input
                if len(df) != len(input_df):
                    logger.warning("Output file has different number of rows than input. Recreating from input.")
                    df = input_df.copy()
                else:
                    logger.info("Using existing output file for continued testing")
            else:
                logger.info("Creating new output file from input")
                df = input_df.copy()
        except Exception as e:
            logger.warning(f"Failed to load output file, creating from input: {e}")
            df = input_df.copy()

        # Determine row range
        if start_row is None:
            start_row = self.progress['last_processed_row']

        if end_row is None:
            end_row = len(df)

        start_row = max(0, start_row)
        end_row = min(len(df), end_row)

        if start_row >= end_row:
            logger.info("No rows to process")
            return True

        logger.info(f"Processing rows {start_row} to {end_row-1} "
                   f"(total: {end_row - start_row} rows)")

        # Initialize progress if starting fresh
        if self.progress['start_time'] is None:
            self.progress['start_time'] = datetime.now().isoformat()

        # Add result columns if they don't exist
        result_columns = ['test_status', 'test_success', 'test_response_time',
                         'test_error', 'test_timestamp', 'test_delivery_count',
                         'test_has_delivery', 'test_error_type', 'test_detailed_error',
                         'test_service_methods', 'test_capacity_available', 'test_full_response',
                         'test_ship_nodes', 'test_merge_nodes', 'test_merge_node_lists',
                         'test_transport_methods', 'test_service_item_ids', 'test_earliest_ship_date']
        for col in result_columns:
            if col not in df.columns:
                df[col] = None

        # Process in batches
        for batch_start in range(start_row, end_row, batch_size):
            batch_end = min(batch_start + batch_size, end_row)
            batch_df = df.iloc[batch_start:batch_end]

            # Extract zipcodes for this batch
            zipcodes = batch_df['ZipCode'].tolist()

            logger.info(f"Processing batch: rows {batch_start}-{batch_end-1} "
                       f"({len(zipcodes)} zipcodes)")

            # Process batch
            batch_results = self.process_zipcode_batch(zipcodes, batch_start)

            # Update DataFrame with results
            for row_index, result in batch_results:
                df.at[row_index, 'test_status'] = result.get('status_code', 'ERROR')
                df.at[row_index, 'test_success'] = result.get('success', False)
                df.at[row_index, 'test_response_time'] = result.get('response_time')
                df.at[row_index, 'test_timestamp'] = result.get('timestamp')
                df.at[row_index, 'test_delivery_count'] = result.get('delivery_services_count')
                df.at[row_index, 'test_has_delivery'] = result.get('has_delivery_options')

                # Handle delivery service details
                service_details = result.get('delivery_service_details', [])
                delivery_lines_info = result.get('delivery_lines_info', [])

                if service_details:
                    service_methods = [s['service_id'] for s in service_details]
                    df.at[row_index, 'test_service_methods'] = ', '.join(service_methods)
                else:
                    df.at[row_index, 'test_service_methods'] = None

                # Handle delivery lines information (shipNode, mergeNode, etc.)
                if delivery_lines_info:
                    ship_nodes = [line['ship_node'] for line in delivery_lines_info if line['ship_node'] != 'Unknown']
                    merge_nodes = [line['merge_node'] for line in delivery_lines_info if line['merge_node'] != 'Unknown']
                    merge_node_lists = [line['merge_node_list'] for line in delivery_lines_info if line['merge_node_list'] != 'Unknown']
                    transport_methods = [line['transport_method'] for line in delivery_lines_info if line['transport_method'] != 'Unknown']
                    service_item_ids = [line['service_item_id'] for line in delivery_lines_info if line['service_item_id'] != 'Unknown']

                    df.at[row_index, 'test_ship_nodes'] = ', '.join(set(ship_nodes)) if ship_nodes else None
                    df.at[row_index, 'test_merge_nodes'] = ', '.join(set(merge_nodes)) if merge_nodes else None
                    df.at[row_index, 'test_merge_node_lists'] = ', '.join(set(merge_node_lists)) if merge_node_lists else None
                    df.at[row_index, 'test_transport_methods'] = ', '.join(set(transport_methods)) if transport_methods else None
                    df.at[row_index, 'test_service_item_ids'] = ', '.join(set(service_item_ids)) if service_item_ids else None

                    # Get earliest ship date from service details
                    earliest_dates = []
                    for service in service_details:
                        for solution in service.get('solutions', []):
                            if solution.get('earliest_ship_date') != 'Unknown':
                                earliest_dates.append(solution['earliest_ship_date'])
                    df.at[row_index, 'test_earliest_ship_date'] = min(earliest_dates) if earliest_dates else None
                else:
                    df.at[row_index, 'test_ship_nodes'] = None
                    df.at[row_index, 'test_merge_nodes'] = None
                    df.at[row_index, 'test_merge_node_lists'] = None
                    df.at[row_index, 'test_transport_methods'] = None
                    df.at[row_index, 'test_service_item_ids'] = None
                    df.at[row_index, 'test_earliest_ship_date'] = None

                # Store full API response (truncated for Excel)
                response_data = result.get('response_data')
                if response_data:
                    # Store a summary of the response, not the full JSON to avoid Excel cell limits
                    response_summary = {
                        'delivery_services': len(response_data.get('deliveryServices', [])),
                        'has_services': len(response_data.get('deliveryServices', [])) > 0
                    }
                    if 'deliveryServices' in response_data and response_data['deliveryServices']:
                        first_service = response_data['deliveryServices'][0]
                        response_summary['first_service_method'] = first_service.get('deliveryMethod', 'Unknown')
                        response_summary['time_windows'] = len(first_service.get('timeWindows', []))

                    df.at[row_index, 'test_full_response'] = str(response_summary)
                else:
                    df.at[row_index, 'test_full_response'] = None

                # Handle error information
                error = result.get('error')
                if error:
                    if isinstance(error, dict):
                        df.at[row_index, 'test_error_type'] = error.get('exception_type') or error.get('status_code', 'Unknown')
                        df.at[row_index, 'test_error'] = result.get('error_summary', str(error))
                        df.at[row_index, 'test_detailed_error'] = str(error)
                    else:
                        df.at[row_index, 'test_error'] = str(error)
                        df.at[row_index, 'test_error_type'] = 'Unknown'
                        df.at[row_index, 'test_detailed_error'] = str(error)
                else:
                    df.at[row_index, 'test_error'] = None
                    df.at[row_index, 'test_error_type'] = None
                    df.at[row_index, 'test_detailed_error'] = None

            # Save progress and results periodically
            self.save_progress()

            # Save updated output Excel file
            try:
                df.to_excel(self.output_excel_file, index=False)
                logger.info(f"Updated output Excel file with batch results: {self.output_excel_file}")
            except Exception as e:
                logger.error(f"Failed to save output Excel file: {e}")

            # Add delay between batches to avoid overwhelming the API
            if batch_end < end_row:
                time.sleep(1)

        # Final save
        self.save_progress()

        # Flush any remaining detailed responses
        self.flush_detailed_responses()

        # Print summary
        logger.info("Test completed!")
        logger.info(f"Total processed: {self.progress['total_processed']}")
        logger.info(f"Successful: {self.progress['successful']}")
        logger.info(f"Failed: {self.progress['failed']}")
        logger.info(f"Cache hits: {len(self.result_cache)}")
        logger.info(f"Detailed responses saved to: {self.response_log_file}")

        return True


def main():
    parser = argparse.ArgumentParser(description='Test delivery arrangement API')
    parser.add_argument('--start-row', type=int, help='Start row index (0-based)')
    parser.add_argument('--end-row', type=int, help='End row index (exclusive)')
    parser.add_argument('--batch-size', type=int, default=50,
                       help='Batch size for processing (default: 50)')
    parser.add_argument('--reset-progress', action='store_true',
                       help='Reset progress and start from beginning')

    args = parser.parse_args()

    try:
        tester = DeliveryTester()

        if args.reset_progress:
            logger.info("Resetting progress...")
            if os.path.exists(tester.progress_file):
                os.remove(tester.progress_file)
            tester.progress = tester.load_progress()

        success = tester.run_test(
            start_row=args.start_row,
            end_row=args.end_row,
            batch_size=args.batch_size
        )

        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
