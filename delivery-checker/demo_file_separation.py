#!/usr/bin/env python3
"""
Demo script to show file separation functionality
"""

import pandas as pd
import os

def demo_file_separation():
    """Demonstrate the file separation functionality"""
    print("🔍 File Separation Demo")
    print("=" * 50)
    
    # Show current configuration
    print("📋 Current Configuration (.env):")
    with open('.env', 'r') as f:
        for line in f:
            if line.strip() and not line.startswith('#'):
                print(f"   {line.strip()}")
    
    print("\n📁 File Status:")
    
    # Check input file
    input_file = os.getenv('INPUT_EXCEL_FILE', 'masterfile-0805.xlsx')
    if os.path.exists(input_file):
        input_df = pd.read_excel(input_file)
        test_cols_input = [col for col in input_df.columns if col.startswith('test_')]
        print(f"✅ Input file ({input_file}):")
        print(f"   Rows: {len(input_df)}")
        print(f"   Columns: {len(input_df.columns)}")
        print(f"   Test columns: {len(test_cols_input)} ({'CLEAN' if len(test_cols_input) == 0 else 'CONTAINS TEST DATA'})")
    else:
        print(f"❌ Input file ({input_file}) not found")
    
    # Check output file
    output_file = os.getenv('OUTPUT_EXCEL_FILE', 'delivery_test_results.xlsx')
    if os.path.exists(output_file):
        output_df = pd.read_excel(output_file)
        test_cols_output = [col for col in output_df.columns if col.startswith('test_')]
        tested_rows = output_df['test_status'].notna().sum() if 'test_status' in output_df.columns else 0
        print(f"✅ Output file ({output_file}):")
        print(f"   Rows: {len(output_df)}")
        print(f"   Columns: {len(output_df.columns)}")
        print(f"   Test columns: {len(test_cols_output)}")
        print(f"   Tested rows: {tested_rows}")
    else:
        print(f"❌ Output file ({output_file}) not found")
    
    # Show other files
    other_files = ['progress.json', 'api_responses.json', 'delivery_test.log']
    print(f"\n📄 Other Files:")
    for file in other_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} ({size} bytes)")
        else:
            print(f"❌ {file} (not found)")
    
    print(f"\n💡 Usage Examples:")
    print("# Test with default files:")
    print("python3 delivery_test.py --start-row 0 --end-row 10")
    print()
    print("# Test with custom files (modify .env first):")
    print("INPUT_EXCEL_FILE=my_zipcodes.xlsx")
    print("OUTPUT_EXCEL_FILE=my_results.xlsx")
    print()
    print("# Analyze results:")
    print("python3 analyze_results.py")
    
    print(f"\n🔒 File Protection:")
    print("✅ Original input file is never modified")
    print("✅ All test results go to separate output file")
    print("✅ Can resume testing by running script again")
    print("✅ Input file can be shared safely without test data")

if __name__ == '__main__':
    from dotenv import load_dotenv
    load_dotenv()
    demo_file_separation()
