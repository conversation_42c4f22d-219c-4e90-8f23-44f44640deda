#!/usr/bin/env python3
"""
Demo script to showcase timestamped results functionality
"""

import os
import json
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def demo_timestamped_results():
    """Demonstrate the timestamped results functionality"""
    print("🕒 Timestamped Results Demo")
    print("=" * 60)
    
    print("📋 Key Features:")
    print("✅ Each test run creates a unique timestamped folder")
    print("✅ Original input file is never modified")
    print("✅ All output files include timestamps")
    print("✅ Easy to manage multiple test runs")
    print("✅ Built-in cleanup functionality")
    print()
    
    # Show configuration
    print("🔧 Configuration:")
    print(f"   Input file: {os.getenv('INPUT_EXCEL_FILE', 'masterfile-0805.xlsx')}")
    print(f"   Output directory: {os.getenv('OUTPUT_DIR', 'results')}")
    print(f"   Concurrency: {os.getenv('CONCURRENCY', '5')}")
    print()
    
    # Show current results
    output_dir = os.getenv('OUTPUT_DIR', 'results')
    if os.path.exists(output_dir):
        test_dirs = [d for d in os.listdir(output_dir) if d.startswith('test_')]
        print(f"📊 Current Test Results: {len(test_dirs)} runs found")
        
        if test_dirs:
            test_dirs.sort(reverse=True)  # Latest first
            for i, test_dir in enumerate(test_dirs[:3]):  # Show latest 3
                test_path = os.path.join(output_dir, test_dir)
                timestamp_str = test_dir.replace('test_', '')
                
                try:
                    timestamp = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                    formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    formatted_time = timestamp_str
                
                print(f"   {'🔥' if i == 0 else '📋'} {test_dir} ({formatted_time})")
                
                # Show file sizes
                files = os.listdir(test_path)
                for file in files:
                    file_path = os.path.join(test_path, file)
                    size = os.path.getsize(file_path)
                    print(f"      📄 {file} ({size} bytes)")
    else:
        print("📊 No test results found yet")
    
    print()
    print("💡 Usage Examples:")
    print()
    
    print("1️⃣ Run a new test (creates timestamped folder):")
    print("   python3 delivery_test.py --start-row 0 --end-row 10")
    print()
    
    print("2️⃣ List all test results:")
    print("   python3 list_results.py")
    print()
    
    print("3️⃣ Analyze latest results:")
    print("   python3 analyze_results.py")
    print()
    
    print("4️⃣ Clean old results (keep latest 5):")
    print("   python3 list_results.py clean 5")
    print()
    
    print("🗂️ File Structure Example:")
    print("delivery-checker/")
    print("├── masterfile-0805.xlsx          # Input file (never modified)")
    print("├── results/                      # Output directory")
    print("│   ├── test_20250805_132933/     # Test run 1")
    print("│   │   ├── delivery_test_results_20250805_132933.xlsx")
    print("│   │   ├── api_responses_20250805_132933.json")
    print("│   │   ├── progress_20250805_132933.json")
    print("│   │   └── delivery_test_20250805_132933.log")
    print("│   └── test_20250805_141522/     # Test run 2")
    print("│       ├── delivery_test_results_20250805_141522.xlsx")
    print("│       ├── api_responses_20250805_141522.json")
    print("│       ├── progress_20250805_141522.json")
    print("│       └── delivery_test_20250805_141522.log")
    print("├── delivery_test.py              # Main script")
    print("├── analyze_results.py            # Analysis script")
    print("└── list_results.py               # Results management")
    print()
    
    print("🔒 Benefits:")
    print("✅ No risk of overwriting previous results")
    print("✅ Easy to compare different test runs")
    print("✅ Automatic file organization")
    print("✅ Safe to run multiple tests in parallel")
    print("✅ Built-in result management and cleanup")
    print("✅ Original data files remain untouched")
    
    print()
    print("🚀 Ready to test 30,000+ zipcodes safely!")

def show_file_naming_convention():
    """Show the file naming convention"""
    print("\n📝 File Naming Convention:")
    print("=" * 40)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    print(f"Timestamp format: {timestamp}")
    print("   YYYY = Year (4 digits)")
    print("   MM   = Month (2 digits)")
    print("   DD   = Day (2 digits)")
    print("   HH   = Hour (24-hour format)")
    print("   MM   = Minute")
    print("   SS   = Second")
    print()
    
    print("File naming examples:")
    print(f"   📁 Folder: test_{timestamp}/")
    print(f"   📊 Excel: delivery_test_results_{timestamp}.xlsx")
    print(f"   📡 API log: api_responses_{timestamp}.json")
    print(f"   📈 Progress: progress_{timestamp}.json")
    print(f"   📝 Log: delivery_test_{timestamp}.log")

def main():
    """Main demo function"""
    demo_timestamped_results()
    show_file_naming_convention()

if __name__ == '__main__':
    main()
