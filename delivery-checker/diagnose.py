#!/usr/bin/env python3
"""
Diagnostic script to test API connectivity and authentication
"""

import os
import sys
import json
import requests
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_env_config():
    """Test environment configuration"""
    print("🔧 Testing Environment Configuration")
    print("=" * 50)
    
    client_id = os.getenv('CLIENT_ID')
    client_secret = os.getenv('CLIENT_SECRET')
    
    if not client_id:
        print("❌ CLIENT_ID not found in .env file")
        return False
    else:
        print(f"✅ CLIENT_ID found: {client_id[:10]}...")
    
    if not client_secret:
        print("❌ CLIENT_SECRET not found in .env file")
        return False
    else:
        print(f"✅ CLIENT_SECRET found: {client_secret[:10]}...")
    
    print(f"✅ CONCURRENCY: {os.getenv('CONCURRENCY', '5')}")
    print(f"✅ EXCEL_FILE: {os.getenv('EXCEL_FILE', 'masterfile-0805.xlsx')}")
    
    return True

def test_token_request():
    """Test OAuth token request"""
    print("\n🔐 Testing OAuth Token Request")
    print("=" * 50)
    
    client_id = os.getenv('CLIENT_ID')
    client_secret = os.getenv('CLIENT_SECRET')
    
    token_url = "https://login.microsoftonline.com/720b637a-655a-40cf-816a-f22f40755c2c/oauth2/v2.0/token"
    
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    data = {
        'scope': 'https://api.prod.cn.ingka.com/.default',
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret
    }
    
    print(f"🌐 Token URL: {token_url}")
    print(f"📋 Request data: {dict(data)}")
    print("📤 Sending token request...")
    
    try:
        response = requests.post(token_url, headers=headers, data=data, timeout=30)
        
        print(f"📊 Response Status: {response.status_code} {response.reason}")
        print(f"⏱️  Response Time: {response.elapsed.total_seconds():.2f}s")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            token_data = response.json()
            print("✅ Token request successful!")
            print(f"🔑 Access Token: {token_data['access_token'][:50]}...")
            print(f"⏰ Expires In: {token_data.get('expires_in', 'N/A')} seconds")
            print(f"🎯 Token Type: {token_data.get('token_type', 'N/A')}")
            return token_data['access_token']
        else:
            print("❌ Token request failed!")
            print(f"📄 Response Text: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Token request exception: {e}")
        return None

def test_api_call(token, test_zipcode="311300"):
    """Test API call with a sample zipcode"""
    print(f"\n🚚 Testing Delivery API Call (zipcode: {test_zipcode})")
    print("=" * 50)
    
    api_url = "https://private-api.ingka.prodcn.ikea.com/cfb/customer-promise/cn/delivery-arrangement"
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    payload = {
        "checkCapacity": True,
        "checkInventory": True,
        "businessUnit": {
            "type": "STO",
            "code": "1228"
        },
        "channelReferences": {
            "sellingChannelName": "DeliveryChecker"
        },
        "shipToAddress": {
            "country": "CN",
            "zipCode": str(test_zipcode)
        },
        "itemLines": {
            "itemLine": [
                {
                    "itemType": "ART",
                    "itemNo": "10534224",
                    "id": "1",
                    "requiredQty": 1,
                    "unitOfMeasure": "Meter"
                }
            ]
        },
        "serviceTypes": {
            "serviceType": [
                {
                    "id": "HOME_DELIVERY"
                }
            ]
        },
        "useLeadTimeOrchestration": True,
        "checkNoStock": True
    }
    
    print(f"🌐 API URL: {api_url}")
    print(f"📋 Request Headers: {headers}")
    print(f"📦 Request Payload: {json.dumps(payload, indent=2)}")
    print("📤 Sending API request...")
    
    try:
        response = requests.post(api_url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 Response Status: {response.status_code} {response.reason}")
        print(f"⏱️  Response Time: {response.elapsed.total_seconds():.2f}s")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API call successful!")
            response_data = response.json()
            print(f"📄 Response Data: {json.dumps(response_data, indent=2)}")
            
            # Analyze response
            if 'deliveryServices' in response_data:
                delivery_services = response_data['deliveryServices']
                print(f"🚚 Delivery Services Found: {len(delivery_services)}")
                for i, service in enumerate(delivery_services):
                    print(f"  Service {i+1}: {service.get('deliveryMethod', 'Unknown')}")
            else:
                print("⚠️  No delivery services found in response")
                
            return True
        else:
            print("❌ API call failed!")
            print(f"📄 Response Text: {response.text}")
            
            # Try to parse error response
            try:
                error_data = response.json()
                print(f"📄 Error JSON: {json.dumps(error_data, indent=2)}")
            except:
                print("📄 Response is not valid JSON")
                
            return False
            
    except Exception as e:
        print(f"❌ API call exception: {e}")
        import traceback
        print(f"📄 Traceback: {traceback.format_exc()}")
        return False

def test_excel_data():
    """Test Excel data loading"""
    print(f"\n📊 Testing Excel Data Loading")
    print("=" * 50)
    
    excel_file = os.getenv('EXCEL_FILE', 'masterfile-0805.xlsx')
    
    try:
        df = pd.read_excel(excel_file)
        print(f"✅ Excel file loaded: {excel_file}")
        print(f"📊 Rows: {len(df)}, Columns: {len(df.columns)}")
        print(f"📋 Columns: {df.columns.tolist()}")
        
        if 'ZipCode' in df.columns:
            print(f"✅ ZipCode column found")
            zipcodes = df['ZipCode'].head(10).tolist()
            print(f"📦 Sample zipcodes: {zipcodes}")
            return zipcodes
        else:
            print("❌ ZipCode column not found!")
            return []
            
    except Exception as e:
        print(f"❌ Excel loading failed: {e}")
        return []

def main():
    """Run all diagnostic tests"""
    print("🔍 Delivery Checker Diagnostic Tool")
    print("=" * 60)
    print(f"⏰ Started at: {datetime.now()}")
    
    # Test 1: Environment configuration
    if not test_env_config():
        print("\n❌ Environment configuration failed. Please check your .env file.")
        return 1
    
    # Test 2: Excel data
    sample_zipcodes = test_excel_data()
    if not sample_zipcodes:
        print("\n❌ Excel data loading failed.")
        return 1
    
    # Test 3: OAuth token
    token = test_token_request()
    if not token:
        print("\n❌ OAuth token request failed. Please check your credentials.")
        return 1
    
    # Test 4: API call with sample zipcode
    test_zipcode = sample_zipcodes[0] if sample_zipcodes else "311300"
    api_success = test_api_call(token, test_zipcode)
    
    print(f"\n{'='*60}")
    print("🏁 Diagnostic Summary")
    print("=" * 60)
    
    if api_success:
        print("✅ All tests passed! The delivery checker should work correctly.")
        print(f"\n💡 You can now run:")
        print(f"   python delivery_test.py --start-row 0 --end-row 20")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
