#!/usr/bin/env python3
"""
Example usage of the delivery tester script

This script demonstrates different ways to use the delivery tester.
"""

import os
import sys
import subprocess
import time

def run_command(cmd, description):
    """Run a command and display the result"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.stdout:
            print("STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("Command timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    """Demonstrate different usage scenarios"""
    
    print("Delivery Tester Usage Examples")
    print("=" * 60)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("\n⚠️  WARNING: .env file not found!")
        print("Please create .env file with your CLIENT_ID and CLIENT_SECRET")
        print("You can copy from .env.sample and update the values")
        print("\nFor demonstration purposes, we'll show the commands but they may fail without proper credentials.")
        input("\nPress Enter to continue with examples...")
    
    examples = [
        {
            'cmd': 'python3 delivery_test.py --help',
            'desc': 'Show help and available options'
        },
        {
            'cmd': 'python3 delivery_test.py --start-row 0 --end-row 5 --batch-size 2',
            'desc': 'Test first 5 rows with batch size of 2'
        },
        {
            'cmd': 'python3 delivery_test.py --start-row 10 --end-row 20',
            'desc': 'Test rows 10-19 (continuing from where we left off)'
        },
        {
            'cmd': 'python3 delivery_test.py --reset-progress --start-row 0 --end-row 3',
            'desc': 'Reset progress and test first 3 rows'
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n\n📋 Example {i}: {example['desc']}")
        
        if i > 1:  # Skip actual API calls for examples 2+ unless user confirms
            response = input(f"\nThis will make actual API calls. Continue? (y/N): ")
            if response.lower() != 'y':
                print("Skipped.")
                continue
        
        success = run_command(example['cmd'], example['desc'])
        
        if not success and i > 1:
            print("\n⚠️  Command failed. This might be due to:")
            print("1. Missing or incorrect CLIENT_ID/CLIENT_SECRET in .env")
            print("2. Network connectivity issues")
            print("3. API endpoint not accessible")
            
            response = input("\nContinue with remaining examples? (y/N): ")
            if response.lower() != 'y':
                break
        
        if i < len(examples):
            time.sleep(2)  # Brief pause between examples
    
    print(f"\n\n{'='*60}")
    print("Examples completed!")
    print("\n📁 Check these files for results:")
    print("- delivery_test.log (detailed logs)")
    print("- progress.json (execution progress)")
    print("- masterfile-0805.xlsx (updated with test results)")
    
    print("\n💡 Tips:")
    print("- Use --start-row and --end-row to test specific ranges")
    print("- Use --batch-size to control memory usage and API load")
    print("- Use --reset-progress to start fresh")
    print("- Monitor progress with: tail -f delivery_test.log")
    print("- Check progress with: cat progress.json")

if __name__ == '__main__':
    main()
