#!/usr/bin/env python3
"""
Find zipcodes that lost leading zeros and need to be retested
"""

import pandas as pd
import os
from dotenv import load_dotenv

load_dotenv()

def find_missing_leading_zeros():
    """Find zipcodes that lost leading zeros"""
    
    print('🔍 查找丢失前导0的ZipCode')
    print('=' * 50)
    
    # 读取原始输入文件（保持字符串格式）
    input_file = os.getenv('INPUT_EXCEL_FILE', 'masterfile-0805.xlsx')
    print(f'📁 读取输入文件: {input_file}')
    
    try:
        df_input = pd.read_excel(input_file, dtype={'ZipCode': str})
        print(f'✅ 输入文件读取成功: {len(df_input)}行')
    except Exception as e:
        print(f'❌ 读取输入文件失败: {e}')
        return []
    
    # 查找以0开头的zipcode
    zero_start_mask = df_input['ZipCode'].str.startswith('0')
    zero_start_zipcodes = df_input[zero_start_mask]
    zero_start_count = len(zero_start_zipcodes)
    
    print(f'🔍 原始文件中以0开头的zipcode: {zero_start_count}个')
    
    if zero_start_count == 0:
        print('✅ 没有以0开头的zipcode，无需修复')
        return []
    
    # 显示一些示例
    print('前10个以0开头的zipcode示例:')
    for i, (idx, row) in enumerate(zero_start_zipcodes.head(10).iterrows()):
        zipcode = row['ZipCode']
        print(f'  行{idx}: {zipcode} (长度{len(zipcode)})')
    
    # 读取最新的测试结果
    output_dir = os.getenv('OUTPUT_DIR', 'results')
    if not os.path.exists(output_dir):
        print('❌ 没有找到结果目录')
        return list(zero_start_zipcodes.index)
    
    test_dirs = sorted([d for d in os.listdir(output_dir) if d.startswith('test_')])
    if not test_dirs:
        print('❌ 没有找到测试结果')
        return list(zero_start_zipcodes.index)
    
    latest_dir = test_dirs[-1]
    results_path = os.path.join(output_dir, latest_dir)
    excel_files = [f for f in os.listdir(results_path) if f.endswith('.xlsx')]
    
    if not excel_files:
        print('❌ 没有找到结果Excel文件')
        return list(zero_start_zipcodes.index)
    
    output_file = os.path.join(results_path, excel_files[0])
    print(f'📁 读取输出文件: {output_file}')
    
    try:
        # 读取输出文件（不指定dtype，看看实际存储的格式）
        df_output = pd.read_excel(output_file)
        print(f'✅ 输出文件读取成功: {len(df_output)}行')
    except Exception as e:
        print(f'❌ 读取输出文件失败: {e}')
        return list(zero_start_zipcodes.index)
    
    # 分析需要重新测试的行
    need_retest = []
    
    print(f'\n🔍 分析需要重新测试的zipcode:')
    
    for idx, row in zero_start_zipcodes.iterrows():
        original_zipcode = row['ZipCode']  # 带前导0的正确格式
        
        # 检查输出文件中对应行的zipcode
        if idx < len(df_output):
            output_zipcode = str(df_output.iloc[idx]['ZipCode'])
            
            # 如果输出的zipcode不等于原始zipcode，说明丢失了前导0
            if output_zipcode != original_zipcode:
                need_retest.append({
                    'row_index': idx,
                    'original_zipcode': original_zipcode,
                    'output_zipcode': output_zipcode,
                    'test_success': df_output.iloc[idx].get('test_success', False)
                })
    
    print(f'📊 分析结果:')
    print(f'  原始文件中以0开头的zipcode: {zero_start_count}个')
    print(f'  需要重新测试的zipcode: {len(need_retest)}个')
    
    if need_retest:
        print(f'\n🚨 需要重新测试的zipcode示例 (前10个):')
        for i, item in enumerate(need_retest[:10]):
            print(f'  行{item["row_index"]}: {item["output_zipcode"]} -> {item["original_zipcode"]} '
                  f'(测试成功: {item["test_success"]})')
        
        # 保存需要重新测试的行号列表
        retest_rows = [item['row_index'] for item in need_retest]
        retest_file = 'retest_rows.txt'
        with open(retest_file, 'w') as f:
            for row in retest_rows:
                f.write(f'{row}\n')
        
        print(f'\n💾 需要重新测试的行号已保存到: {retest_file}')
        print(f'📝 重新测试命令建议:')
        
        # 生成分批重新测试的命令
        batch_size = 100
        for i in range(0, len(retest_rows), batch_size):
            batch_start = retest_rows[i]
            batch_end = retest_rows[min(i + batch_size - 1, len(retest_rows) - 1)] + 1
            print(f'  python3 delivery_test.py --start-row {batch_start} --end-row {batch_end} --batch-size 20')
        
        return retest_rows
    else:
        print('✅ 所有zipcode格式正确，无需重新测试')
        return []

if __name__ == '__main__':
    retest_rows = find_missing_leading_zeros()
    
    if retest_rows:
        print(f'\n🔧 下一步操作:')
        print('1. 运行上述命令重新测试受影响的zipcode')
        print('2. 新的测试将使用正确的字符串格式zipcode')
        print('3. 检查重新测试的结果')
    else:
        print(f'\n✅ 无需进一步操作')
