#!/usr/bin/env python3
"""
List all test results with timestamps
"""

import os
import json
import pandas as pd
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def list_test_results():
    """List all test results with details"""
    print("📊 Test Results Summary")
    print("=" * 60)
    
    output_dir = os.getenv('OUTPUT_DIR', 'results')
    
    if not os.path.exists(output_dir):
        print(f"❌ Results directory not found: {output_dir}")
        print("Run a test first to create results.")
        return
    
    # Find all test directories
    test_dirs = [d for d in os.listdir(output_dir) if d.startswith('test_')]
    
    if not test_dirs:
        print(f"❌ No test results found in: {output_dir}")
        return
    
    print(f"📁 Results directory: {output_dir}")
    print(f"📊 Found {len(test_dirs)} test runs:")
    print()
    
    # Sort by timestamp (newest first)
    test_dirs.sort(reverse=True)
    
    for i, test_dir in enumerate(test_dirs):
        test_path = os.path.join(output_dir, test_dir)
        
        # Extract timestamp from directory name
        timestamp_str = test_dir.replace('test_', '')
        try:
            timestamp = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
            formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        except:
            formatted_time = timestamp_str
        
        print(f"{'🔥' if i == 0 else '📋'} Test Run {i+1}: {test_dir}")
        print(f"   📅 Time: {formatted_time}")
        
        # Check files in the directory
        files = os.listdir(test_path)
        
        # Check Excel results
        excel_files = [f for f in files if f.endswith('.xlsx')]
        if excel_files:
            excel_file = os.path.join(test_path, excel_files[0])
            try:
                df = pd.read_excel(excel_file)
                tested_rows = df['test_status'].notna().sum() if 'test_status' in df.columns else 0
                successful_rows = df['test_success'].sum() if 'test_success' in df.columns else 0
                print(f"   📊 Excel: {tested_rows} tested, {successful_rows} successful")
            except Exception as e:
                print(f"   📊 Excel: Error reading file - {e}")
        
        # Check API responses
        json_files = [f for f in files if f.startswith('api_responses_') and f.endswith('.json')]
        if json_files:
            json_file = os.path.join(test_path, json_files[0])
            try:
                with open(json_file, 'r') as f:
                    responses = json.load(f)
                print(f"   📡 API calls: {len(responses)} total")
            except Exception as e:
                print(f"   📡 API calls: Error reading file - {e}")
        
        # Check progress
        progress_files = [f for f in files if f.startswith('progress_') and f.endswith('.json')]
        if progress_files:
            progress_file = os.path.join(test_path, progress_files[0])
            try:
                with open(progress_file, 'r') as f:
                    progress = json.load(f)
                print(f"   📈 Progress: Row {progress.get('last_processed_row', 0)}, "
                      f"{progress.get('successful', 0)} successful, {progress.get('failed', 0)} failed")
            except Exception as e:
                print(f"   📈 Progress: Error reading file - {e}")
        
        # Check log file
        log_files = [f for f in files if f.startswith('delivery_test_') and f.endswith('.log')]
        if log_files:
            log_file = os.path.join(test_path, log_files[0])
            try:
                log_size = os.path.getsize(log_file)
                print(f"   📝 Log: {log_size} bytes")
            except Exception as e:
                print(f"   📝 Log: Error reading file - {e}")
        
        print(f"   📁 Path: {test_path}")
        print()
    
    # Show latest results info
    if test_dirs:
        latest_dir = os.path.join(output_dir, test_dirs[0])
        print("💡 To analyze the latest results:")
        print(f"   python3 analyze_results.py")
        print()
        print("💡 To continue testing from the latest run:")
        print(f"   python3 delivery_test.py")

def clean_old_results(keep_count=5):
    """Clean old test results, keeping only the specified number"""
    print(f"🧹 Cleaning old results (keeping latest {keep_count})")
    print("=" * 50)
    
    output_dir = os.getenv('OUTPUT_DIR', 'results')
    
    if not os.path.exists(output_dir):
        print("❌ No results directory found")
        return
    
    # Find all test directories
    test_dirs = [d for d in os.listdir(output_dir) if d.startswith('test_')]
    
    if len(test_dirs) <= keep_count:
        print(f"✅ Only {len(test_dirs)} results found, nothing to clean")
        return
    
    # Sort by timestamp (oldest first for deletion)
    test_dirs.sort()
    
    # Delete old directories
    to_delete = test_dirs[:-keep_count]
    
    for test_dir in to_delete:
        test_path = os.path.join(output_dir, test_dir)
        try:
            import shutil
            shutil.rmtree(test_path)
            print(f"🗑️  Deleted: {test_dir}")
        except Exception as e:
            print(f"❌ Failed to delete {test_dir}: {e}")
    
    print(f"✅ Cleanup completed. Kept {keep_count} latest results.")

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'clean':
        keep_count = int(sys.argv[2]) if len(sys.argv) > 2 else 5
        clean_old_results(keep_count)
    else:
        list_test_results()

if __name__ == '__main__':
    main()
