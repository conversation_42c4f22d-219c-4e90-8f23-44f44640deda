#!/usr/bin/env python3
"""
Speed analysis for delivery test results
"""

import json
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

def analyze_speed():
    """Analyze the speed of the latest test run"""
    
    # Find latest results directory
    output_dir = os.getenv('OUTPUT_DIR', 'results')
    test_dirs = [d for d in os.listdir(output_dir) if d.startswith('test_')]
    
    if not test_dirs:
        print("❌ No test results found")
        return
    
    latest_dir = sorted(test_dirs)[-1]
    results_path = os.path.join(output_dir, latest_dir)
    
    # Find API responses file
    api_files = [f for f in os.listdir(results_path) if f.startswith('api_responses_')]
    if not api_files:
        print("❌ No API response file found")
        return
    
    api_file = os.path.join(results_path, api_files[0])
    
    print('📊 100行测试速度分析报告')
    print('=' * 60)
    print(f'📁 分析文件: {api_file}')
    
    # Load API responses
    with open(api_file, 'r') as f:
        responses = json.load(f)
    
    if not responses:
        print("❌ No responses found")
        return
    
    # Calculate timing from log timestamps
    start_time = datetime.fromisoformat(responses[0]['timestamp'])
    end_time = datetime.fromisoformat(responses[-1]['timestamp'])
    total_duration = (end_time - start_time).total_seconds()
    
    print(f'\n⏰ 测试时间范围:')
    print(f'   开始时间: {start_time.strftime("%H:%M:%S")}')
    print(f'   结束时间: {end_time.strftime("%H:%M:%S")}')
    print(f'   总耗时: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)')
    
    print(f'\n📈 速度统计:')
    print(f'   测试行数: {len(responses)}')
    print(f'   平均每行耗时: {total_duration/len(responses):.2f}秒')
    print(f'   每分钟处理行数: {len(responses)/(total_duration/60):.1f}行/分钟')
    print(f'   每小时处理行数: {len(responses)/(total_duration/3600):.0f}行/小时')
    
    # API response time analysis
    response_times = [r['response_time'] for r in responses if r.get('response_time')]
    if response_times:
        print(f'\n🌐 API响应时间分析:')
        print(f'   平均响应时间: {sum(response_times)/len(response_times):.3f}秒')
        print(f'   最快响应: {min(response_times):.3f}秒')
        print(f'   最慢响应: {max(response_times):.3f}秒')
        
        # Response time distribution
        fast_responses = sum(1 for t in response_times if t < 0.5)
        medium_responses = sum(1 for t in response_times if 0.5 <= t < 1.0)
        slow_responses = sum(1 for t in response_times if t >= 1.0)
        
        print(f'\n📊 响应时间分布:')
        print(f'   快速 (<0.5s): {fast_responses} ({fast_responses/len(response_times)*100:.1f}%)')
        print(f'   中等 (0.5-1.0s): {medium_responses} ({medium_responses/len(response_times)*100:.1f}%)')
        print(f'   较慢 (>1.0s): {slow_responses} ({slow_responses/len(response_times)*100:.1f}%)')
    
    # Success rate
    successful = sum(1 for r in responses if r.get('success'))
    print(f'\n✅ 成功率分析:')
    print(f'   成功调用: {successful}/{len(responses)} ({successful/len(responses)*100:.1f}%)')
    print(f'   失败调用: {len(responses)-successful}/{len(responses)} ({(len(responses)-successful)/len(responses)*100:.1f}%)')
    
    # Delivery services analysis
    with_delivery = sum(1 for r in responses if r.get('response_data') and 
                       'serviceTypes' in r['response_data'] and 
                       r['response_data']['serviceTypes'].get('serviceType'))
    
    print(f'\n🚚 配送服务分析:')
    print(f'   有配送服务: {with_delivery}/{len(responses)} ({with_delivery/len(responses)*100:.1f}%)')
    print(f'   无配送服务: {len(responses)-with_delivery}/{len(responses)} ({(len(responses)-with_delivery)/len(responses)*100:.1f}%)')
    
    # Estimate for full dataset
    full_dataset_size = 30898
    if total_duration > 0:
        estimated_time_hours = (full_dataset_size / len(responses)) * (total_duration / 3600)
        estimated_time_days = estimated_time_hours / 24
        
        print(f'\n🚀 全量数据预估 (30,898行):')
        print(f'   预估总耗时: {estimated_time_hours:.1f}小时 ({estimated_time_days:.1f}天)')
        print(f'   建议分批执行: 每批1000-2000行')
        print(f'   当前并发数: {os.getenv("CONCURRENCY", "10")}')
        
        # Recommendations based on performance
        if estimated_time_hours > 24:
            print(f'\n💡 性能优化建议:')
            print(f'   - 考虑增加并发数到15-20')
            print(f'   - 分多个时间段执行（避免长时间运行）')
            print(f'   - 可以并行运行多个实例处理不同的行范围')
        
        print(f'\n📅 执行计划建议:')
        batches = [1000, 2000, 5000]
        for batch_size in batches:
            batch_time = (batch_size / len(responses)) * (total_duration / 3600)
            num_batches = (full_dataset_size + batch_size - 1) // batch_size
            print(f'   每批{batch_size}行: {batch_time:.1f}小时/批, 共{num_batches}批')

if __name__ == '__main__':
    analyze_speed()
