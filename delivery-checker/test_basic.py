#!/usr/bin/env python3
"""
Basic test script to verify the delivery tester functionality
"""

import os
import sys
import pandas as pd
from delivery_test import DeliveryTester

def test_excel_loading():
    """Test Excel file loading"""
    print("Testing Excel file loading...")
    try:
        df = pd.read_excel('masterfile-0805.xlsx')
        print(f"✓ Excel file loaded successfully: {len(df)} rows, {len(df.columns)} columns")
        print(f"✓ ZipCode column found: {df['ZipCode'].dtype}")
        print(f"✓ Sample zipcodes: {df['ZipCode'].head().tolist()}")
        return True
    except Exception as e:
        print(f"✗ Failed to load Excel file: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    print("\nTesting configuration loading...")
    
    # Create a test .env file if it doesn't exist
    if not os.path.exists('.env'):
        print("Creating test .env file...")
        with open('.env', 'w') as f:
            f.write("CLIENT_ID=test_client_id\n")
            f.write("CLIENT_SECRET=test_client_secret\n")
            f.write("CONCURRENCY=3\n")
            f.write("EXCEL_FILE=masterfile-0805.xlsx\n")
            f.write("PROGRESS_FILE=test_progress.json\n")
    
    try:
        tester = DeliveryTester()
        print(f"✓ Configuration loaded successfully")
        print(f"✓ Concurrency: {tester.concurrency}")
        print(f"✓ Excel file: {tester.excel_file}")
        print(f"✓ Progress file: {tester.progress_file}")
        return True
    except Exception as e:
        print(f"✗ Failed to load configuration: {e}")
        return False

def test_progress_management():
    """Test progress management"""
    print("\nTesting progress management...")
    try:
        tester = DeliveryTester()
        
        # Test initial progress
        initial_progress = tester.progress
        print(f"✓ Initial progress loaded: {initial_progress}")
        
        # Test progress saving
        tester.progress['test_field'] = 'test_value'
        tester.save_progress()
        print("✓ Progress saved successfully")
        
        # Test progress loading
        tester2 = DeliveryTester()
        if tester2.progress.get('test_field') == 'test_value':
            print("✓ Progress loaded correctly after save")
        else:
            print("✗ Progress not loaded correctly")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Failed progress management test: {e}")
        return False

def test_zipcode_extraction():
    """Test zipcode extraction from Excel"""
    print("\nTesting zipcode extraction...")
    try:
        df = pd.read_excel('masterfile-0805.xlsx')
        
        # Test first 10 rows
        test_df = df.head(10)
        zipcodes = test_df['ZipCode'].tolist()
        
        print(f"✓ Extracted {len(zipcodes)} zipcodes from first 10 rows")
        print(f"✓ Sample zipcodes: {zipcodes[:5]}")
        
        # Check for duplicates in sample
        unique_zipcodes = list(set(zipcodes))
        if len(unique_zipcodes) < len(zipcodes):
            print(f"✓ Found {len(zipcodes) - len(unique_zipcodes)} duplicate zipcodes in sample")
        else:
            print("✓ No duplicates in sample")
            
        return True
    except Exception as e:
        print(f"✗ Failed zipcode extraction test: {e}")
        return False

def main():
    """Run all basic tests"""
    print("Running basic functionality tests...\n")
    
    tests = [
        test_excel_loading,
        test_config_loading,
        test_progress_management,
        test_zipcode_extraction
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All basic tests passed! The script should work correctly.")
        print("\nNext steps:")
        print("1. Update .env file with your actual CLIENT_ID and CLIENT_SECRET")
        print("2. Run: python delivery_test.py --start-row 0 --end-row 10")
        print("3. Check the results in the Excel file and logs")
    else:
        print("✗ Some tests failed. Please check the configuration and files.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
